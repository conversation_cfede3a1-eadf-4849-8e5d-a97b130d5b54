import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Button,
  Alert,
  Snackbar,
  Chip,
  CircularProgress,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  CheckCircle as CheckCircleIcon,
  CreditCard as CreditCardIcon,
} from '@mui/icons-material';
import dayjs from 'dayjs';

import { vendorCreditService } from '../services/vendor-credit.service';
import { formatCurrency } from '../../../shared/utils/formatters';
import api from '../../../services/api';

interface GRNReturnItem {
  grn_return_item_id: number;
  product: number | null;
  product_name: string;
  quantity_returned: number;
  unit_cost: number;
  total_cost: number;
  return_reason: string;
}

interface GRNReturn {
  grn_return_id: number;
  grn_return_number: string;
  original_grn: number;
  original_grn_number: string;
  vendor_name: string;
  return_date: string;
  status: string;
  total_value: number;
  warehouse_name: string;
  items: GRNReturnItem[];
}

interface SnackbarState {
  open: boolean;
  message: string;
  severity: 'success' | 'error' | 'warning' | 'info';
}

const CreateVendorCreditFromGRNReturnPage: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const grnReturnId = searchParams.get('grn_return_id');

  const [grnReturn, setGrnReturn] = useState<GRNReturn | null>(null);
  const [loading, setLoading] = useState(true);
  const [creating, setCreating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [snackbar, setSnackbar] = useState<SnackbarState>({
    open: false,
    message: '',
    severity: 'success'
  });

  useEffect(() => {
    if (grnReturnId) {
      loadGRNReturn();
    } else {
      setError('No GRN Return ID provided');
      setLoading(false);
    }
  }, [grnReturnId]);

  const loadGRNReturn = async () => {
    try {
      setLoading(true);
      setError(null);

      // Get GRN Return details from inventory service
      const response = await api.get(`/inventory/grn-returns/${grnReturnId}/`);
      setGrnReturn(response.data);
    } catch (err) {
      console.error('Error loading GRN Return:', err);
      setError(err instanceof Error ? err.message : 'Failed to load GRN Return details');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateCredit = async () => {
    if (!grnReturn) return;

    try {
      setCreating(true);
      setError(null);

      // Create vendor credit from GRN return
      const creditData = {
        grn_return_id: grnReturn.grn_return_id,
        credit_reason: 'DEFECTIVE',
        notes: `Credit for goods return: ${grnReturn.grn_return_number}`,
      };

      await vendorCreditService.createCreditFromReturn(grnReturn.grn_return_id, creditData);

      setSnackbar({
        open: true,
        message: 'Vendor credit created successfully!',
        severity: 'success'
      });

      // Navigate to vendor credits list after a short delay
      setTimeout(() => {
        navigate('/dashboard/purchases/vendor-credits');
      }, 1500);

    } catch (err) {
      console.error('Error creating vendor credit:', err);
      setError(err instanceof Error ? err.message : 'Failed to create vendor credit');
    } finally {
      setCreating(false);
    }
  };

  const handleBack = () => {
    navigate('/dashboard/inventory/grn-returns');
  };

  const handleCloseSnackbar = () => {
    setSnackbar(prev => ({ ...prev, open: false }));
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'APPROVED': return 'success';
      case 'RETURNED': return 'warning';
      case 'POSTED': return 'info';
      case 'DRAFT': return 'default';
      case 'CANCELLED': return 'error';
      default: return 'default';
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
        <Typography sx={{ ml: 2 }}>Loading GRN Return details...</Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
        <Button variant="outlined" startIcon={<ArrowBackIcon />} onClick={handleBack}>
          Back to GRN Returns
        </Button>
      </Box>
    );
  }

  if (!grnReturn) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="warning">GRN Return not found</Alert>
        <Button variant="outlined" startIcon={<ArrowBackIcon />} onClick={handleBack}>
          Back to GRN Returns
        </Button>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <IconButton onClick={handleBack} sx={{ mr: 2 }}>
          <ArrowBackIcon />
        </IconButton>
        <Box sx={{ flexGrow: 1 }}>
          <Typography variant="h4" component="h1">
            Create Vendor Credit
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Create vendor credit from GRN Return: {grnReturn.grn_return_number}
          </Typography>
        </Box>
        <Button
          variant="contained"
          startIcon={creating ? <CircularProgress size={20} /> : <CreditCardIcon />}
          onClick={handleCreateCredit}
          disabled={creating || grnReturn.status !== 'APPROVED'}
          size="large"
        >
          {creating ? 'Creating Credit...' : 'Create Credit'}
        </Button>
      </Box>

      {/* GRN Return Details */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                GRN Return Information
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">
                    Return Number
                  </Typography>
                  <Typography variant="body1" fontWeight="medium">
                    {grnReturn.grn_return_number}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">
                    Original GRN
                  </Typography>
                  <Typography variant="body1">
                    {grnReturn.original_grn_number}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">
                    Vendor
                  </Typography>
                  <Typography variant="body1">
                    {grnReturn.vendor_name}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">
                    Return Date
                  </Typography>
                  <Typography variant="body1">
                    {dayjs(grnReturn.return_date).format('MMM DD, YYYY')}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">
                    Status
                  </Typography>
                  <Chip
                    label={grnReturn.status}
                    color={getStatusColor(grnReturn.status)}
                    size="small"
                  />
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">
                    Warehouse
                  </Typography>
                  <Typography variant="body1">
                    {grnReturn.warehouse_name}
                  </Typography>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Credit Summary
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <Typography variant="body2" color="text.secondary">
                    Total Credit Amount
                  </Typography>
                  <Typography variant="h5" color="primary" fontWeight="bold">
                    {formatCurrency(grnReturn.total_value)}
                  </Typography>
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="body2" color="text.secondary">
                    Items to Credit
                  </Typography>
                  <Typography variant="body1">
                    {grnReturn.items.length} items
                  </Typography>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Items Table */}
      <Card sx={{ mt: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Items to Credit
          </Typography>
          {/* Items will be added here */}
        </CardContent>
      </Card>

      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default CreateVendorCreditFromGRNReturnPage;
