# Generated manually for vendor bill credit functionality
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('gl', '0001_initial'),
        ('sales', '0012_remove_invoice_estimate_salesorder_deliverynote_payment'),
        ('inventory', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('purchase', '0011_remove_vendor_payment'),
    ]

    operations = [
        # Add credit tracking fields to VendorBill
        migrations.AddField(
            model_name='vendorbill',
            name='total_credits',
            field=models.DecimalField(decimal_places=2, default=0.0, help_text='Total credits applied', max_digits=15),
        ),
        migrations.AddField(
            model_name='vendorbill',
            name='net_balance',
            field=models.DecimalField(decimal_places=2, default=0.0, help_text='Balance due minus credits', max_digits=15),
        ),
        
        # Create VendorBillCredit model
        migrations.CreateModel(
            name='VendorBillCredit',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('credit_id', models.UUIDField(default=uuid.uuid4, unique=True)),
                ('credit_number', models.CharField(help_text='Auto-generated credit number (VCREDIT-000001)', max_length=50, unique=True)),
                ('credit_amount', models.DecimalField(decimal_places=2, help_text='Credit amount before tax', max_digits=15)),
                ('tax_amount', models.DecimalField(decimal_places=2, default=0.0, help_text='Tax amount on credit', max_digits=15)),
                ('total_credit', models.DecimalField(decimal_places=2, help_text='Total credit amount including tax', max_digits=15)),
                ('applied_amount', models.DecimalField(decimal_places=2, default=0.0, help_text='Amount already applied to bills', max_digits=15)),
                ('remaining_credit', models.DecimalField(decimal_places=2, default=0.0, help_text='Remaining credit available for application', max_digits=15)),
                ('credit_type', models.CharField(choices=[('goods_return', 'Goods Return'), ('price_adjustment', 'Price Adjustment'), ('damaged_goods', 'Damaged Goods'), ('billing_error', 'Billing Error'), ('discount', 'Discount'), ('other', 'Other')], default='goods_return', max_length=20)),
                ('credit_date', models.DateField(default=django.utils.timezone.now)),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('approved', 'Approved'), ('applied', 'Applied'), ('cancelled', 'Cancelled')], default='draft', max_length=20)),
                ('reason', models.TextField(help_text='Reason for credit')),
                ('notes', models.TextField(blank=True, help_text='Additional notes', null=True)),
                ('approved_date', models.DateTimeField(blank=True, null=True)),
                ('applied_date', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='vendor_credits_approved', to=settings.AUTH_USER_MODEL)),
                ('applied_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='vendor_credits_applied', to=settings.AUTH_USER_MODEL)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='vendor_credits_created', to=settings.AUTH_USER_MODEL)),
                ('goods_return_note', models.ForeignKey(help_text='Goods return note that generated this credit', on_delete=django.db.models.deletion.CASCADE, related_name='vendor_credits', to='inventory.goodsreturnnote')),
                ('original_vendor_bill', models.ForeignKey(blank=True, help_text='Original bill being credited (if applicable)', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='credits', to='purchase.vendorbill')),
                ('vendor', models.ForeignKey(help_text='Vendor providing the credit', on_delete=django.db.models.deletion.CASCADE, related_name='vendor_credits', to='contacts.contact')),
            ],
            options={
                'verbose_name': 'Vendor Bill Credit',
                'verbose_name_plural': 'Vendor Bill Credits',
                'db_table': 'purchase_vendor_bill_credits',
                'ordering': ['-credit_date', '-created_at'],
            },
        ),
        
        # Create VendorBillCreditItem model
        migrations.CreateModel(
            name='VendorBillCreditItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity_credited', models.DecimalField(decimal_places=4, help_text='Quantity being credited', max_digits=15)),
                ('unit_price', models.DecimalField(decimal_places=2, help_text='Unit price for credit calculation', max_digits=15)),
                ('line_total', models.DecimalField(decimal_places=2, help_text='Line total before tax', max_digits=15)),
                ('tax_rate', models.DecimalField(decimal_places=2, default=0.0, help_text='Tax rate percentage', max_digits=5)),
                ('tax_amount', models.DecimalField(decimal_places=2, default=0.0, help_text='Tax amount for this line', max_digits=15)),
                ('description', models.TextField(blank=True, help_text='Description of credit item', null=True)),
                ('line_order', models.PositiveIntegerField(default=0, help_text='Order of line item')),
                ('credit', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='line_items', to='purchase.vendorbillcredit')),
                ('credit_account', models.ForeignKey(blank=True, help_text='GL account to credit (defaults to product expense account)', null=True, on_delete=django.db.models.deletion.SET_NULL, to='gl.account')),
                ('product', models.ForeignKey(help_text='Product being credited', on_delete=django.db.models.deletion.CASCADE, related_name='vendor_credit_items', to='sales.product')),
                ('return_item', models.ForeignKey(help_text='Original return item being credited', on_delete=django.db.models.deletion.CASCADE, related_name='credit_items', to='inventory.goodsreturnnoteitem')),
            ],
            options={
                'verbose_name': 'Vendor Bill Credit Item',
                'verbose_name_plural': 'Vendor Bill Credit Items',
                'db_table': 'purchase_vendor_bill_credit_items',
                'ordering': ['line_order', 'id'],
            },
        ),
    ]
