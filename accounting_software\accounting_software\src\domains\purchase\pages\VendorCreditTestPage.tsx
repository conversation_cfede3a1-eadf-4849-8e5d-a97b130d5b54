import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  TextField,
  Alert,
  Card,
  CardContent,
  Grid,
} from '@mui/material';
import { PageContainer, PageHeader } from '../../../layouts/components/PageComponents';
import { vendorCreditService } from '../services/vendor-credit.service';

const VendorCreditTestPage: React.FC = () => {
  const [result, setResult] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [returnNoteId, setReturnNoteId] = useState<string>('');

  const testGetCredits = async () => {
    setLoading(true);
    try {
      const response = await vendorCreditService.getVendorCredits();
      setResult(`✅ GET Credits Success: Found ${response.results.length} credits\n${JSON.stringify(response, null, 2)}`);
    } catch (error) {
      setResult(`❌ GET Credits Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const testCreateFromReturn = async () => {
    if (!returnNoteId) {
      setResult('❌ Please enter a return note ID');
      return;
    }

    setLoading(true);
    try {
      const response = await vendorCreditService.createCreditFromReturn(parseInt(returnNoteId), {
        credit_reason: 'DEFECTIVE',
        notes: 'Test credit creation from return note',
      });
      setResult(`✅ Create from Return Success:\n${JSON.stringify(response, null, 2)}`);
    } catch (error) {
      setResult(`❌ Create from Return Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const testCreateRegular = async () => {
    if (!returnNoteId) {
      setResult('❌ Please enter a return note ID');
      return;
    }

    setLoading(true);
    try {
      const response = await vendorCreditService.createVendorCredit({
        original_grn_return: parseInt(returnNoteId),
        credit_date: new Date().toISOString().split('T')[0],
        credit_reason: 'DEFECTIVE',
        notes: 'Test regular credit creation',
        items: [],
      });
      setResult(`✅ Create Regular Success:\n${JSON.stringify(response, null, 2)}`);
    } catch (error) {
      setResult(`❌ Create Regular Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const testStats = async () => {
    setLoading(true);
    try {
      const response = await vendorCreditService.getVendorCreditStats();
      setResult(`✅ Stats Success:\n${JSON.stringify(response, null, 2)}`);
    } catch (error) {
      setResult(`❌ Stats Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const testCreditableReturns = async () => {
    setLoading(true);
    try {
      const response = await vendorCreditService.getCreditableReturns();
      setResult(`✅ Creditable Returns Success: Found ${response.length} returns\n${JSON.stringify(response, null, 2)}`);
    } catch (error) {
      setResult(`❌ Creditable Returns Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <PageContainer>
      <PageHeader title="Vendor Credit API Test" />

      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Test Controls
              </Typography>
              
              <TextField
                fullWidth
                label="Return Note ID"
                value={returnNoteId}
                onChange={(e) => setReturnNoteId(e.target.value)}
                type="number"
                sx={{ mb: 2 }}
                helperText="Enter the ID of a goods return note to test with"
              />

              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                <Button
                  variant="contained"
                  onClick={testGetCredits}
                  disabled={loading}
                >
                  Test GET Credits
                </Button>
                
                <Button
                  variant="contained"
                  onClick={testCreateFromReturn}
                  disabled={loading || !returnNoteId}
                >
                  Test Create from Return
                </Button>
                
                <Button
                  variant="contained"
                  onClick={testCreateRegular}
                  disabled={loading || !returnNoteId}
                >
                  Test Create Regular
                </Button>
                
                <Button
                  variant="contained"
                  onClick={testStats}
                  disabled={loading}
                >
                  Test Stats
                </Button>
                
                <Button
                  variant="contained"
                  onClick={testCreditableReturns}
                  disabled={loading}
                >
                  Test Creditable Returns
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Test Results
              </Typography>
              
              {loading && (
                <Alert severity="info">
                  Testing API endpoint...
                </Alert>
              )}
              
              <Paper sx={{ p: 2, mt: 2, backgroundColor: '#f5f5f5' }}>
                <pre style={{ whiteSpace: 'pre-wrap', fontSize: '12px' }}>
                  {result || 'Click a test button to see results...'}
                </pre>
              </Paper>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </PageContainer>
  );
};

export default VendorCreditTestPage;
