from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator, MaxValueValidator
from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from django.utils import timezone
from decimal import Decimal
import uuid


class PaymentTerm(models.Model):
    """Payment terms that can be used across vendors, purchase orders, etc."""
    
    name = models.CharField(max_length=100, unique=True, help_text="E.g., 'Net 30', 'Due on Receipt'")
    code = models.CharField(max_length=50, unique=True, help_text="E.g., 'net_30', 'due_on_receipt'")
    days = models.PositiveIntegerField(help_text="Number of days from invoice date")
    description = models.TextField(blank=True, null=True, help_text="Optional description")
    is_default = models.BooleanField(default=False, help_text="Is this the default payment term?")
    is_active = models.BooleanField(default=True, help_text="Is this payment term active?")
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='purchase_payment_terms_created')
    
    class Meta:
        db_table = 'purchase_payment_terms'
        ordering = ['days', 'name']
        verbose_name = 'Payment Term'
        verbose_name_plural = 'Payment Terms'
    
    def __str__(self):
        return f"{self.name} ({self.days} days)"
    
    def save(self, *args, **kwargs):
        # Ensure only one default payment term
        if self.is_default:
            PaymentTerm.objects.filter(is_default=True).update(is_default=False)
        super().save(*args, **kwargs)


# Vendor model is now in contacts app - using contacts.Vendor instead


class PurchaseOrder(models.Model):
    """Purchase Order model following QuickBooks structure"""
    
    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('pending', 'Pending'),
        ('sent', 'Sent'),
        ('acknowledged', 'Acknowledged'),
        ('partial', 'Partially Received'),
        ('received', 'Received'),
        ('closed', 'Closed'),
        ('cancelled', 'Cancelled'),
    ]
    
    # Basic Information
    po_id = models.UUIDField(default=uuid.uuid4, unique=True)
    po_number = models.CharField(max_length=50, unique=True)
    vendor = models.ForeignKey('contacts.Contact', on_delete=models.SET_NULL, null=True, blank=True, related_name='purchase_orders', help_text='Vendor from contacts system')
    
    # Dates
    po_date = models.DateField()
    expected_date = models.DateField(blank=True, null=True)
    
    # Buyer Information
    buyer_name = models.CharField(max_length=200, blank=True, null=True, help_text="Name of the buyer/purchaser")
    buyer_email = models.EmailField(blank=True, null=True, help_text="Email of the buyer/purchaser")
    buyer_phone = models.CharField(max_length=20, blank=True, null=True, help_text="Phone of the buyer/purchaser")
    
    # Financial Information
    subtotal = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    discount_percent = models.DecimalField(max_digits=5, decimal_places=2, default=0.00)
    discount_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    tax_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    total_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    amount_received = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    balance_due = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    
    # Settings
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')
    payment_terms = models.CharField(max_length=20, blank=True, null=True)
    
    # Additional Information
    reference_number = models.CharField(max_length=100, blank=True, null=True)
    memo = models.TextField(blank=True, null=True, help_text="Internal memo")
    notes = models.TextField(blank=True, null=True, help_text="Notes to vendor")
    
    # Shipping Information
    ship_to_address = models.TextField(blank=True, null=True)
    
    # Email Tracking
    email_sent = models.BooleanField(default=False)
    email_sent_date = models.DateTimeField(blank=True, null=True)
    acknowledged_date = models.DateTimeField(blank=True, null=True)
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='purchase_orders_created')
    
    class Meta:
        db_table = 'purchase_orders'
        ordering = ['-po_date', '-created_at']
    
    def __str__(self):
        vendor_name = self.vendor.name if self.vendor else "No Vendor"
        return f"PO {self.po_number} - {vendor_name}"
    
    def calculate_totals(self):
        """Calculate totals from line items"""
        from decimal import Decimal
        
        line_items = self.line_items.all()
        
        # Calculate subtotal from line items
        self.subtotal = sum(item.line_total for item in line_items) or Decimal('0.00')
        
        # Calculate discount amount
        self.discount_amount = self.subtotal * (Decimal(str(self.discount_percent)) / 100)
        
        # Calculate tax amount from line items
        self.tax_amount = sum(item.tax_amount for item in line_items) or Decimal('0.00')
        
        # Calculate total amount
        self.total_amount = self.subtotal - self.discount_amount + self.tax_amount
        
        # Calculate balance due
        self.balance_due = self.total_amount - Decimal(str(self.amount_received))

    def save(self, *args, **kwargs):
        from decimal import Decimal
        
        # Auto-generate PO number if not provided
        if not self.po_number:
            last_po = PurchaseOrder.objects.filter(
                po_number__startswith='PO-'
            ).order_by('-created_at').first()
            
            if last_po:
                try:
                    last_number = int(last_po.po_number.split('-')[1])
                    new_number = last_number + 1
                except (ValueError, IndexError):
                    new_number = 1
            else:
                new_number = 1
            
            self.po_number = f'PO-{new_number:06d}'
        
        # Ensure all decimal fields are Decimal type
        self.subtotal = Decimal(str(self.subtotal))
        self.discount_percent = Decimal(str(self.discount_percent))
        self.discount_amount = Decimal(str(self.discount_amount))
        self.tax_amount = Decimal(str(self.tax_amount))
        self.total_amount = Decimal(str(self.total_amount))
        self.amount_received = Decimal(str(self.amount_received))
        
        # Calculate balance due
        self.balance_due = self.total_amount - self.amount_received
        
        # Update status based on actual quantity receipt, not financial amounts
        # Don't auto-update status in save method - let it be controlled explicitly
        # by the GRN posting process to avoid conflicts
        
        # Only auto-set status for new POs or if explicitly changing financial amounts
        if self.pk is None:  # New PO
            if self.status not in ['draft', 'pending', 'sent', 'acknowledged', 'partial', 'received']:
                self.status = 'draft'
        
        super().save(*args, **kwargs)


class PurchaseOrderLineItem(models.Model):
    """Purchase Order line items for products and services"""
    
    purchase_order = models.ForeignKey(PurchaseOrder, on_delete=models.CASCADE, related_name='line_items')
    
    # Product/Service Link
    product = models.ForeignKey(
        'sales.Product', 
        on_delete=models.CASCADE, 
        related_name='purchase_line_items',
        null=True,
        blank=True,
        help_text="Link to product master for GL integration"
    )
    
    # Item details
    description = models.TextField(help_text="Auto-filled from product, can be overridden")
    quantity = models.DecimalField(max_digits=10, decimal_places=2, default=1.00)
    unit_of_measure = models.CharField(max_length=20, default='pcs', help_text="Unit of measure (kg, L, pcs, etc.)")
    unit_price = models.DecimalField(max_digits=15, decimal_places=2, default=0.00, help_text="Purchase cost per unit")
    discount_percent = models.DecimalField(max_digits=5, decimal_places=2, default=0.00)
    line_total = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    
    # Receiving information
    quantity_received = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    quantity_pending = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    
    # Tax information
    taxable = models.BooleanField(default=True)
    tax_rate = models.DecimalField(max_digits=5, decimal_places=2, default=0.00)
    tax_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    
    # Additional information
    notes = models.TextField(blank=True, null=True, help_text="Line item specific notes")
    
    # Line item order
    line_order = models.PositiveIntegerField(default=0)
    
    class Meta:
        db_table = 'purchase_order_line_items'
        ordering = ['line_order']
    
    def save(self, *args, **kwargs):
        from decimal import Decimal
        
        # Auto-fill description from product if not provided
        if not self.description and self.product:
            self.description = self.product.name
        
        # Ensure all numeric fields are Decimal
        self.quantity = Decimal(str(self.quantity))
        self.unit_price = Decimal(str(self.unit_price))
        self.discount_percent = Decimal(str(self.discount_percent))
        self.tax_rate = Decimal(str(self.tax_rate))
        self.quantity_received = Decimal(str(self.quantity_received or 0))
        
        # Calculate line total
        subtotal = self.quantity * self.unit_price
        discount_amount = subtotal * (self.discount_percent / 100)
        self.line_total = subtotal - discount_amount
        
        # Calculate tax
        if self.taxable:
            self.tax_amount = self.line_total * (self.tax_rate / 100)
        else:
            self.tax_amount = Decimal('0.00')
        
        # Calculate pending quantity
        self.quantity_pending = self.quantity - self.quantity_received
        
        super().save(*args, **kwargs)
    
    def create_receipt_journal_entry(self):
        """Create GL journal entry when goods are received"""
        if not self.product or self.quantity_received <= 0:
            return None
            
        from gl.models import JournalEntry, JournalEntryLine
        
        # Calculate received amount
        received_value = self.quantity_received * self.unit_price
        
        # Create journal entry for goods receipt
        journal_entry = JournalEntry.objects.create(
            reference_number=f"GR-{self.purchase_order.po_number}-{self.id}",
            description=f"Goods Receipt: {self.product.name}",
            total_amount=received_value,
            entry_type='purchase_receipt'
        )
        
        # Debit Inventory Asset Account
        if self.product.inventory_asset_account_gl:
            JournalEntryLine.objects.create(
                journal_entry=journal_entry,
                account=self.product.inventory_asset_account_gl,
                description=f"Inventory Receipt: {self.product.name}",
                debit_amount=received_value,
                credit_amount=0
            )
        
        # Credit Accounts Payable (or Cash if paid immediately)
        # Note: This would need a payable account reference
        # For now, we'll leave this as a placeholder
        
        return journal_entry
    
    def update_product_inventory(self):
        """Update product inventory quantities and cost"""
        if self.product and self.quantity_received > 0:
            # Update inventory quantity
            self.product.quantity_on_hand += self.quantity_received
            
            # Update cost price with weighted average
            total_current_value = self.product.quantity_on_hand * self.product.cost_price
            new_purchase_value = self.quantity_received * self.unit_price
            total_quantity = self.product.quantity_on_hand + self.quantity_received
            
            if total_quantity > 0:
                self.product.cost_price = (total_current_value + new_purchase_value) / total_quantity
            
            self.product.quantity_on_hand = total_quantity
            self.product.save()


class VendorBill(models.Model):
    """Vendor Bill model for Accounts Payable - comprehensive ERP implementation"""

    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('posted', 'Posted'),
        ('paid', 'Paid'),
    ]

    # Basic Information
    bill_id = models.UUIDField(default=uuid.uuid4, unique=True)
    bill_number = models.CharField(max_length=50, unique=True, help_text="Unique identifier (e.g., 'VBILL-2023-001')")
    vendor = models.ForeignKey('contacts.Contact', on_delete=models.SET_NULL, null=True, blank=True, related_name='vendor_bills', help_text='Link to vendor master')

    # Dates
    bill_date = models.DateField(help_text="Date of invoice issuance")
    due_date = models.DateField(help_text="Payment due date")

    # Document Links - for different bill creation scenarios
    purchase_order = models.ForeignKey(
        'PurchaseOrder',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='vendor_bills',
        help_text="Linked PO for service bills or PO-based bills"
    )
    grn = models.ForeignKey(
        'inventory.GoodsReceiptNote',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='vendor_bills',
        help_text="Linked GRN for goods receipt bills"
    )
    goods_return_note = models.ForeignKey(
        'inventory.GoodsReturnNote',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='vendor_bills',
        help_text="Linked goods return note for return-based bills"
    )

    # Financial Information
    subtotal = models.DecimalField(max_digits=15, decimal_places=2, default=0.00, help_text="Before tax")
    tax_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0.00, help_text="Calculated tax amount")
    total_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0.00, help_text="Final payable amount")
    amount_paid = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    total_credits = models.DecimalField(max_digits=15, decimal_places=2, default=0.00, help_text="Total credits applied")
    balance_due = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    net_balance = models.DecimalField(max_digits=15, decimal_places=2, default=0.00, help_text="Balance due minus credits")

    # Settings
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')
    payment_terms = models.CharField(max_length=100, blank=True, null=True, help_text="e.g., 'Net 30'")

    # Additional Information
    reference_number = models.CharField(max_length=100, blank=True, null=True, help_text="Vendor's bill/invoice number")
    notes = models.TextField(blank=True, null=True, help_text="Internal notes")

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='vendor_bills_created')

    class Meta:
        db_table = 'purchase_vendor_bills'
        ordering = ['-bill_date', '-created_at']
        verbose_name = 'Vendor Bill'
        verbose_name_plural = 'Vendor Bills'

    def __str__(self):
        vendor_name = self.vendor.name if self.vendor else "No Vendor"
        return f"Bill {self.bill_number} - {vendor_name}"

    def calculate_totals(self):
        """Calculate totals from line items"""
        from decimal import Decimal

        line_items = self.line_items.all()

        # Calculate subtotal from line items
        self.subtotal = sum(item.line_total for item in line_items) or Decimal('0.00')

        # Calculate tax amount from line items
        self.tax_amount = sum(item.tax_amount for item in line_items) or Decimal('0.00')

        # Calculate total amount
        self.total_amount = self.subtotal + self.tax_amount

        # Calculate total credits applied
        self.total_credits = sum(
            credit.applied_amount for credit in self.credits.filter(status='applied')
        ) or Decimal('0.00')

        # Calculate balance due
        self.balance_due = self.total_amount - Decimal(str(self.amount_paid))

        # Calculate net balance (balance due minus credits)
        self.net_balance = self.balance_due - self.total_credits

    def save(self, *args, **kwargs):
        from decimal import Decimal

        # Auto-generate bill number if not provided
        if not self.bill_number:
            last_bill = VendorBill.objects.filter(
                bill_number__startswith='BILL-'
            ).order_by('-created_at').first()

            if last_bill:
                try:
                    last_number = int(last_bill.bill_number.split('-')[1])
                    new_number = last_number + 1
                except (ValueError, IndexError):
                    new_number = 1
            else:
                new_number = 1

            self.bill_number = f'BILL-{new_number:06d}'

        # Ensure all decimal fields are Decimal type
        self.subtotal = Decimal(str(self.subtotal))
        self.tax_amount = Decimal(str(self.tax_amount))
        self.total_amount = Decimal(str(self.total_amount))
        self.amount_paid = Decimal(str(self.amount_paid))

        # Calculate balance due
        self.balance_due = self.total_amount - self.amount_paid

        # Update status based on payment
        from datetime import date
        if self.balance_due <= 0 and self.total_amount > 0:
            self.status = 'paid'
        elif self.due_date < date.today() and self.balance_due > 0:
            self.status = 'overdue'
        elif self.status == 'draft' and self.total_amount > 0:
            self.status = 'pending'

        super().save(*args, **kwargs)

        # Create GL entries if this is a new bill or status changed to posted
        if self.status == 'posted' and self.total_amount > 0:
            self.create_gl_entries()

    def delete(self, *args, **kwargs):
        """Override delete to prevent deletion of posted bills"""
        if self.status == 'posted':
            from django.core.exceptions import ValidationError
            raise ValidationError("Posted vendor bills cannot be deleted. Only draft bills can be deleted.")
        super().delete(*args, **kwargs)

    def create_gl_entries(self):
        """Create General Ledger entries for the vendor bill"""
        try:
            from gl.models import JournalEntry, JournalEntryLine, Account
            from decimal import Decimal

            # Check if GL entries already exist for this bill
            existing_entry = JournalEntry.objects.filter(
                reference_number=f"VB-{self.bill_number}",
                entry_type='vendor_bill'
            ).first()

            if existing_entry:
                return existing_entry  # Already created

            # Get the user who created this bill (or use a default system user)
            created_by_user = getattr(self, 'created_by', None)
            if not created_by_user:
                # Try to get the first admin user as fallback
                from django.contrib.auth.models import User
                created_by_user = User.objects.filter(is_superuser=True).first()
                if not created_by_user:
                    created_by_user = User.objects.first()

            # Create the main journal entry
            journal_entry = JournalEntry.objects.create(
                entry_number=f"VB-{self.bill_number}",
                reference_number=f"VB-{self.bill_number}",
                description=f"Vendor Bill: {self.vendor.name} - {self.bill_number}",
                transaction_date=self.bill_date,
                entry_type='GENERAL',
                source_document_type='BILL',
                source_document_id=str(self.id),
                source_document_reference=self.bill_number,
                status='DRAFT',
                created_by=created_by_user
            )

            # Get or create default accounts
            accounts_payable_account = self.get_or_create_accounts_payable_account()
            sales_tax_payable_account = self.get_or_create_sales_tax_payable_account()

            # Create journal lines for each line item (Debit expense accounts)
            for line_item in self.line_items.all():
                if line_item.line_total > 0:
                    # Get expense account for this line item
                    expense_account = self.get_expense_account_for_line_item(line_item)

                    # Debit the expense account
                    JournalEntryLine.objects.create(
                        journal_entry=journal_entry,
                        account=expense_account,
                        description=f"{line_item.item_description}",
                        debit_amount=line_item.line_total,
                        credit_amount=Decimal('0.00'),
                        line_number=line_item.line_order
                    )

                    # Create tax entry if there's tax on this line item
                    if line_item.tax_amount > 0:
                        JournalEntryLine.objects.create(
                            journal_entry=journal_entry,
                            account=sales_tax_payable_account,
                            description=f"Input Tax - {line_item.item_description}",
                            debit_amount=line_item.tax_amount,
                            credit_amount=Decimal('0.00'),
                            line_number=line_item.line_order + 100  # Offset for tax lines
                        )

            # Credit Accounts Payable for the total amount
            JournalEntryLine.objects.create(
                journal_entry=journal_entry,
                account=accounts_payable_account,
                description=f"Accounts Payable - {self.vendor.name}",
                debit_amount=Decimal('0.00'),
                credit_amount=self.total_amount,
                line_number=999  # Last line
            )

            return journal_entry

        except Exception as e:
            # Log the error but don't fail the vendor bill creation
            print(f"Error creating GL entries for vendor bill {self.bill_number}: {e}")
            return None

    def get_or_create_accounts_payable_account(self):
        """Get or create the Accounts Payable account"""
        from gl.models import Account, AccountType, DetailType

        try:
            # Try to find existing Accounts Payable account
            ap_account = Account.objects.filter(
                account_name__icontains='Accounts Payable'
            ).first()

            if ap_account:
                return ap_account

            # Create Accounts Payable account if it doesn't exist
            liability_type = AccountType.objects.filter(name='Liabilities').first()
            if not liability_type:
                liability_type = AccountType.objects.create(
                    name='Liabilities',
                    normal_balance='CREDIT'
                )

            current_liability_detail = DetailType.objects.filter(
                name='Current Liabilities'
            ).first()
            if not current_liability_detail:
                current_liability_detail = DetailType.objects.create(
                    name='Current Liabilities',
                    account_type=liability_type
                )

            # Get a user for account creation
            from django.contrib.auth.models import User
            user = User.objects.filter(is_superuser=True).first() or User.objects.first()

            ap_account = Account.objects.create(
                account_number='2010',
                account_name='Accounts Payable',
                account_type=liability_type,
                detail_type=current_liability_detail,
                description='Vendor bills and payables',
                created_by=user
            )

            return ap_account

        except Exception as e:
            print(f"Error getting/creating Accounts Payable account: {e}")
            return None

    def get_or_create_sales_tax_payable_account(self):
        """Get or create the Sales Tax Payable account for input tax"""
        from gl.models import Account, AccountType, DetailType

        try:
            # Try to find existing Sales Tax Payable account
            tax_account = Account.objects.filter(
                account_name__icontains='Sales Tax Payable'
            ).first()

            if tax_account:
                return tax_account

            # Create Sales Tax Payable account if it doesn't exist
            liability_type = AccountType.objects.filter(name='Liabilities').first()
            current_liability_detail = DetailType.objects.filter(
                name='Current Liabilities'
            ).first()

            # Get a user for account creation
            from django.contrib.auth.models import User
            user = User.objects.filter(is_superuser=True).first() or User.objects.first()

            tax_account = Account.objects.create(
                account_number='2020',
                account_name='Sales Tax Payable',
                account_type=liability_type,
                detail_type=current_liability_detail,
                description='Sales tax collected and payable',
                created_by=user
            )

            return tax_account

        except Exception as e:
            print(f"Error getting/creating Sales Tax Payable account: {e}")
            return None

    def get_expense_account_for_line_item(self, line_item):
        """Get the appropriate expense account for a line item"""
        from gl.models import Account, AccountType, DetailType

        try:
            # If line item has a specific account code, try to find that account
            if line_item.account_code:
                account = Account.objects.filter(
                    account_number=line_item.account_code.split('-')[0]
                ).first()
                if account:
                    return account

            # If product has an expense account, use that
            if line_item.product and hasattr(line_item.product, 'expense_account'):
                return line_item.product.expense_account

            # Default to COGS or General Expense account
            default_account = Account.objects.filter(
                account_name__icontains='Cost of Goods Sold'
            ).first()

            if not default_account:
                default_account = Account.objects.filter(
                    account_name__icontains='General Expense'
                ).first()

            if not default_account:
                # Create a default expense account
                expense_type = AccountType.objects.filter(name='Expenses').first()
                if not expense_type:
                    expense_type = AccountType.objects.create(
                        name='Expenses',
                        normal_balance='DEBIT'
                    )

                cogs_detail = DetailType.objects.filter(
                    name='Cost of Goods Sold'
                ).first()
                if not cogs_detail:
                    cogs_detail = DetailType.objects.create(
                        name='Cost of Goods Sold',
                        account_type=expense_type
                    )

                # Get a user for account creation
                from django.contrib.auth.models import User
                user = User.objects.filter(is_superuser=True).first() or User.objects.first()

                default_account = Account.objects.create(
                    account_number='5010',
                    account_name='Cost of Goods Sold',
                    account_type=expense_type,
                    detail_type=cogs_detail,
                    description='Cost of goods sold',
                    created_by=user
                )

            return default_account

        except Exception as e:
            print(f"Error getting expense account for line item: {e}")
            return None


class VendorBillItem(models.Model):
    """Vendor Bill line items with GL account integration"""

    vendor_bill = models.ForeignKey(VendorBill, on_delete=models.CASCADE, related_name='line_items')

    # Product/Service Link
    product = models.ForeignKey(
        'sales.Product',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='vendor_bill_items',
        help_text="Link to product master"
    )

    # Item details
    item_description = models.TextField(help_text="Product/service name")
    quantity = models.DecimalField(max_digits=10, decimal_places=2, default=1.00)
    unit_price = models.DecimalField(max_digits=15, decimal_places=2, default=0.00, help_text="Cost per unit")
    line_total = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)

    # Tax information
    tax_rate = models.DecimalField(max_digits=5, decimal_places=2, default=0.00, help_text="e.g., 10%")
    tax_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)

    # GL Integration
    account_code = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        help_text="GL account (e.g., '5010-COGS') - CREDIT: Expense Account"
    )

    # Line item order
    line_order = models.PositiveIntegerField(default=0)

    class Meta:
        db_table = 'purchase_vendor_bill_items'
        ordering = ['line_order']
        verbose_name = 'Vendor Bill Item'
        verbose_name_plural = 'Vendor Bill Items'

    def save(self, *args, **kwargs):
        from decimal import Decimal

        # Auto-fill description from product if not provided
        if not self.item_description and self.product:
            self.item_description = self.product.name

        # Auto-fill account code from product if not provided
        if not self.account_code and self.product:
            # Use product's expense account or default COGS account
            self.account_code = getattr(self.product, 'expense_account_code', '5010-COGS')

        # Ensure all numeric fields are Decimal
        self.quantity = Decimal(str(self.quantity))
        self.unit_price = Decimal(str(self.unit_price))
        self.tax_rate = Decimal(str(self.tax_rate))

        # Calculate line total
        self.line_total = self.quantity * self.unit_price

        # Calculate tax
        self.tax_amount = self.line_total * (self.tax_rate / 100)

        super().save(*args, **kwargs)





class VendorBillCredit(models.Model):
    """
    Vendor Bill Credit Notes - typically created from goods return notes
    Handles credits/refunds from vendors for returned goods
    """

    CREDIT_TYPE_CHOICES = [
        ('goods_return', 'Goods Return'),
        ('price_adjustment', 'Price Adjustment'),
        ('damaged_goods', 'Damaged Goods'),
        ('billing_error', 'Billing Error'),
        ('discount', 'Discount'),
        ('other', 'Other'),
    ]

    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('approved', 'Approved'),
        ('applied', 'Applied'),
        ('cancelled', 'Cancelled'),
    ]

    # Basic Information
    credit_id = models.UUIDField(default=uuid.uuid4, unique=True)
    credit_number = models.CharField(max_length=50, unique=True, help_text="Auto-generated credit number (VCREDIT-000001)")
    vendor = models.ForeignKey(
        'contacts.Contact',
        on_delete=models.CASCADE,
        related_name='vendor_credits',
        help_text='Vendor providing the credit'
    )

    # Source Documents
    original_vendor_bill = models.ForeignKey(
        VendorBill,
        on_delete=models.CASCADE,
        related_name='credits',
        null=True,
        blank=True,
        help_text="Original bill being credited (if applicable)"
    )
    goods_return_note = models.ForeignKey(
        'inventory.GoodsReturnNote',
        on_delete=models.CASCADE,
        related_name='vendor_credits',
        help_text="Goods return note that generated this credit"
    )

    # Financial Information
    credit_amount = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        help_text="Credit amount before tax"
    )
    tax_amount = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="Tax amount on credit"
    )
    total_credit = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        help_text="Total credit amount including tax"
    )
    applied_amount = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="Amount already applied to bills"
    )
    remaining_credit = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="Remaining credit available for application"
    )

    # Details
    credit_type = models.CharField(max_length=20, choices=CREDIT_TYPE_CHOICES, default='goods_return')
    credit_date = models.DateField(default=timezone.now)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')
    reason = models.TextField(help_text="Reason for credit")
    notes = models.TextField(blank=True, null=True, help_text="Additional notes")

    # Application tracking
    approved_date = models.DateTimeField(null=True, blank=True)
    approved_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='vendor_credits_approved'
    )
    applied_date = models.DateTimeField(null=True, blank=True)
    applied_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='vendor_credits_applied'
    )

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name='vendor_credits_created'
    )

    class Meta:
        db_table = 'purchase_vendor_bill_credits'
        ordering = ['-credit_date', '-created_at']
        verbose_name = 'Vendor Bill Credit'
        verbose_name_plural = 'Vendor Bill Credits'

    def __str__(self):
        vendor_name = self.vendor.name if self.vendor else "No Vendor"
        return f"Credit {self.credit_number} - {vendor_name} - ${self.total_credit}"

    def save(self, *args, **kwargs):
        # Auto-generate credit number if not provided
        if not self.credit_number:
            last_credit = VendorBillCredit.objects.filter(
                credit_number__startswith='VCREDIT-'
            ).order_by('-created_at').first()

            if last_credit:
                try:
                    last_number = int(last_credit.credit_number.split('-')[1])
                    new_number = last_number + 1
                except (ValueError, IndexError):
                    new_number = 1
            else:
                new_number = 1

            self.credit_number = f'VCREDIT-{new_number:06d}'

        # Calculate remaining credit
        self.remaining_credit = self.total_credit - self.applied_amount

        super().save(*args, **kwargs)

    def calculate_totals(self):
        """Calculate totals from line items"""
        line_items = self.line_items.all()

        # Calculate credit amount from line items
        self.credit_amount = sum(item.line_total for item in line_items) or Decimal('0.00')

        # Calculate tax amount from line items
        self.tax_amount = sum(item.tax_amount for item in line_items) or Decimal('0.00')

        # Calculate total credit
        self.total_credit = self.credit_amount + self.tax_amount

        # Update remaining credit
        self.remaining_credit = self.total_credit - self.applied_amount


class VendorBillCreditItem(models.Model):
    """
    Line items for vendor bill credits
    Links to specific items from goods return notes
    """

    credit = models.ForeignKey(
        VendorBillCredit,
        on_delete=models.CASCADE,
        related_name='line_items'
    )
    return_item = models.ForeignKey(
        'inventory.GoodsReturnNoteItem',
        on_delete=models.CASCADE,
        related_name='credit_items',
        help_text="Original return item being credited"
    )
    product = models.ForeignKey(
        'sales.Product',
        on_delete=models.CASCADE,
        related_name='vendor_credit_items',
        help_text="Product being credited"
    )

    # Quantities and pricing
    quantity_credited = models.DecimalField(
        max_digits=15,
        decimal_places=4,
        help_text="Quantity being credited"
    )
    unit_price = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        help_text="Unit price for credit calculation"
    )
    line_total = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        help_text="Line total before tax"
    )

    # Tax information
    tax_rate = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="Tax rate percentage"
    )
    tax_amount = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="Tax amount for this line"
    )

    # GL Account for credit (optional - can use product's default)
    credit_account = models.ForeignKey(
        'gl.Account',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text="GL account to credit (defaults to product's expense account)"
    )

    # Additional details
    description = models.TextField(blank=True, null=True, help_text="Description of credit item")
    line_order = models.PositiveIntegerField(default=0, help_text="Order of line item")

    class Meta:
        db_table = 'purchase_vendor_bill_credit_items'
        ordering = ['line_order', 'id']
        verbose_name = 'Vendor Bill Credit Item'
        verbose_name_plural = 'Vendor Bill Credit Items'

    def __str__(self):
        product_name = self.product.name if self.product else "Unknown Product"
        return f"{self.credit.credit_number} - {product_name} ({self.quantity_credited})"

    def save(self, *args, **kwargs):
        # Calculate line total
        self.line_total = self.quantity_credited * self.unit_price

        # Calculate tax amount
        self.tax_amount = self.line_total * (self.tax_rate / 100)

        super().save(*args, **kwargs)


# Signals to update Purchase Order totals when line items change
@receiver(post_save, sender=PurchaseOrderLineItem)
def update_po_totals_on_line_item_save(sender, instance, **kwargs):
    """Update Purchase Order totals when a line item is saved"""
    po = instance.purchase_order
    po.calculate_totals()
    # Use update to avoid triggering the save method again
    PurchaseOrder.objects.filter(pk=po.pk).update(
        subtotal=po.subtotal,
        discount_amount=po.discount_amount,
        tax_amount=po.tax_amount,
        total_amount=po.total_amount,
        balance_due=po.balance_due
    )


@receiver(post_delete, sender=PurchaseOrderLineItem)
def update_po_totals_on_line_item_delete(sender, instance, **kwargs):
    """Update Purchase Order totals when a line item is deleted"""
    po = instance.purchase_order
    po.calculate_totals()
    # Use update to avoid triggering the save method again
    PurchaseOrder.objects.filter(pk=po.pk).update(
        subtotal=po.subtotal,
        discount_amount=po.discount_amount,
        tax_amount=po.tax_amount,
        total_amount=po.total_amount,
        balance_due=po.balance_due
    )


# Signals to update Vendor Bill totals when line items change
@receiver(post_save, sender=VendorBillItem)
def update_vendor_bill_totals_on_line_item_save(sender, instance, **kwargs):
    """Update Vendor Bill totals when a line item is saved"""
    bill = instance.vendor_bill
    bill.calculate_totals()
    # Use update to avoid triggering the save method again
    VendorBill.objects.filter(pk=bill.pk).update(
        subtotal=bill.subtotal,
        tax_amount=bill.tax_amount,
        total_amount=bill.total_amount,
        balance_due=bill.balance_due
    )


@receiver(post_delete, sender=VendorBillItem)
def update_vendor_bill_totals_on_line_item_delete(sender, instance, **kwargs):
    """Update Vendor Bill totals when a line item is deleted"""
    bill = instance.vendor_bill
    bill.calculate_totals()
    # Use update to avoid triggering the save method again
    VendorBill.objects.filter(pk=bill.pk).update(
        subtotal=bill.subtotal,
        tax_amount=bill.tax_amount,
        total_amount=bill.total_amount,
        balance_due=bill.balance_due
    )
