from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone
from decimal import Decimal
import uuid
from datetime import date


class PurchaseOrder(models.Model):
    """Purchase Order model for managing vendor orders"""
    
    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('sent', 'Sent'),
        ('confirmed', 'Confirmed'),
        ('received', 'Received'),
        ('cancelled', 'Cancelled'),
    ]
    
    po_number = models.CharField(max_length=50, unique=True, help_text="Purchase Order Number")
    vendor = models.ForeignKey(
        'contacts.Contact',
        on_delete=models.CASCADE,
        related_name='purchase_orders',
        help_text="Vendor for this purchase order"
    )
    order_date = models.DateField(default=timezone.now)
    expected_delivery_date = models.DateField(null=True, blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')
    
    # Financial fields
    subtotal = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))
    tax_amount = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))
    total_amount = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))
    
    # Additional fields
    notes = models.TextField(blank=True, help_text="Order notes and comments")
    terms_and_conditions = models.TextField(blank=True)
    
    # Audit trail
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name='purchase_orders_created'
    )
    
    class Meta:
        db_table = 'purchase_orders'
        ordering = ['-order_date', '-created_at']
    
    def __str__(self):
        return f"PO {self.po_number} - {self.vendor.name}"


class PurchaseOrderLineItem(models.Model):
    """Line items for purchase orders"""
    
    purchase_order = models.ForeignKey(
        PurchaseOrder,
        on_delete=models.CASCADE,
        related_name='line_items'
    )
    product = models.ForeignKey(
        'sales.Product',
        on_delete=models.CASCADE,
        related_name='purchase_order_items',
        null=True,
        blank=True
    )
    
    # Item details
    item_description = models.CharField(max_length=255, help_text="Description of the item")
    quantity = models.DecimalField(max_digits=15, decimal_places=4, help_text="Quantity ordered")
    unit_price = models.DecimalField(max_digits=15, decimal_places=2, help_text="Unit price")
    line_total = models.DecimalField(max_digits=15, decimal_places=2, help_text="Line total amount")
    
    # Additional fields
    notes = models.TextField(blank=True)
    line_order = models.PositiveIntegerField(default=0, help_text="Order of line item")
    
    class Meta:
        db_table = 'purchase_order_line_items'
        ordering = ['line_order', 'id']


class VendorBill(models.Model):
    """Vendor Bill model for managing accounts payable"""
    
    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('approved', 'Approved'),
        ('paid', 'Paid'),
        ('cancelled', 'Cancelled'),
    ]
    
    bill_number = models.CharField(max_length=50, unique=True, help_text="Bill Number")
    vendor = models.ForeignKey(
        'contacts.Contact',
        on_delete=models.CASCADE,
        related_name='vendor_bills',
        help_text="Vendor for this bill"
    )
    
    # Dates
    bill_date = models.DateField(default=timezone.now)
    due_date = models.DateField()
    
    # Status and references
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')
    reference_number = models.CharField(max_length=100, blank=True, help_text="Vendor's reference number")
    
    # Financial fields
    subtotal = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))
    tax_amount = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))
    total_amount = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))
    amount_paid = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))
    balance_due = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))
    
    # Credit tracking fields
    total_credits = models.DecimalField(
        max_digits=15, 
        decimal_places=2, 
        default=Decimal('0.00'),
        help_text="Total credits applied"
    )
    net_balance = models.DecimalField(
        max_digits=15, 
        decimal_places=2, 
        default=Decimal('0.00'),
        help_text="Balance due minus credits"
    )
    
    # Links to source documents
    purchase_order = models.ForeignKey(
        PurchaseOrder,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='vendor_bills'
    )
    grn = models.ForeignKey(
        'inventory.GoodsReceiptNote',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='vendor_bills'
    )
    
    # Additional fields
    notes = models.TextField(blank=True, help_text="Bill notes and comments")
    
    # Audit trail
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name='vendor_bills_created'
    )
    
    class Meta:
        db_table = 'vendor_bills'
        ordering = ['-bill_date', '-created_at']


class VendorBillItem(models.Model):
    """Line items for vendor bills"""
    
    vendor_bill = models.ForeignKey(
        VendorBill,
        on_delete=models.CASCADE,
        related_name='line_items'
    )
    product = models.ForeignKey(
        'sales.Product',
        on_delete=models.CASCADE,
        related_name='vendor_bill_items',
        null=True,
        blank=True
    )
    
    # Item details
    item_description = models.CharField(max_length=255, help_text="Description of the item")
    quantity = models.DecimalField(max_digits=15, decimal_places=4, help_text="Quantity billed")
    unit_price = models.DecimalField(max_digits=15, decimal_places=2, help_text="Unit price")
    line_total = models.DecimalField(max_digits=15, decimal_places=2, help_text="Line total amount")
    
    # GL integration
    account_code = models.CharField(max_length=20, blank=True, help_text="GL account code")
    
    # Additional fields
    notes = models.TextField(blank=True)
    line_order = models.PositiveIntegerField(default=0, help_text="Order of line item")
    
    class Meta:
        db_table = 'vendor_bill_line_items'
        ordering = ['line_order', 'id']


# VENDOR CREDIT MODELS - Copied from GRN structure but opposite (for credits instead of returns)

class VendorCredit(models.Model):
    """
    Vendor Credits for getting credits from vendors (opposite of GRN Returns)
    Following exact GRN pattern but for vendor credits
    """
    vendor_credit_id = models.AutoField(primary_key=True)
    vendor_credit_number = models.CharField(max_length=50, unique=True, help_text="Vendor Credit reference number")
    
    # Links to original GRN Return and vendor
    original_grn_return = models.ForeignKey(
        'inventory.GoodsReturnNote',
        on_delete=models.CASCADE,
        related_name='vendor_credits',
        help_text="Original GRN Return that generated this credit"
    )
    vendor = models.ForeignKey(
        'contacts.Contact',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='vendor_credits',
        help_text="Vendor providing the credit"
    )
    
    # Credit details
    credit_date = models.DateField(default=date.today)
    
    # Credit reason (same as return reasons but for credit context)
    credit_reason = models.CharField(
        max_length=50,
        choices=[
            ('DEFECTIVE', 'Defective/Damaged'),
            ('WRONG_ITEM', 'Wrong Item Delivered'),
            ('EXCESS_QTY', 'Excess Quantity'),
            ('QUALITY_ISSUE', 'Quality Issue'),
            ('NOT_ORDERED', 'Not Ordered'),
            ('EXPIRED', 'Expired'),
            ('OTHER', 'Other')
        ],
        help_text="Reason for credit"
    )
    
    # Status and notes
    status = models.CharField(
        max_length=20,
        choices=[
            ('DRAFT', 'Draft'),
            ('APPROVED', 'Approved'),
            ('APPLIED', 'Applied to Bills'),
            ('POSTED', 'Posted to GL'),
            ('CANCELLED', 'Cancelled')
        ],
        default='DRAFT'
    )
    
    notes = models.TextField(blank=True, help_text="Credit notes and comments")
    
    # Totals
    total_quantity = models.DecimalField(max_digits=15, decimal_places=4, default=Decimal('0.0000'))
    total_credit_amount = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))
    
    # Credit application information
    applied_amount = models.DecimalField(
        max_digits=15, 
        decimal_places=2, 
        default=Decimal('0.00'),
        help_text="Amount applied to vendor bills"
    )
    remaining_credit = models.DecimalField(
        max_digits=15, 
        decimal_places=2, 
        default=Decimal('0.00'),
        help_text="Remaining credit available"
    )
    credit_applied_date = models.DateField(null=True, blank=True)
    
    # Audit trail
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    approved_at = models.DateTimeField(null=True, blank=True)
    approved_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='vendor_credits_approved'
    )
    posted_at = models.DateTimeField(null=True, blank=True)
    posted_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='vendor_credits_posted'
    )
    
    class Meta:
        db_table = 'vendor_credits'
        ordering = ['-credit_date', '-created_at']
    
    def __str__(self):
        vendor_name = self.vendor.name if self.vendor else "No Vendor"
        return f"VCREDIT {self.vendor_credit_number} - {vendor_name}"
    
    def save(self, *args, **kwargs):
        # Auto-generate credit number if not provided
        if not self.vendor_credit_number:
            last_credit = VendorCredit.objects.filter(
                vendor_credit_number__startswith='VCREDIT-'
            ).order_by('-created_at').first()
            
            if last_credit:
                try:
                    last_number = int(last_credit.vendor_credit_number.split('-')[1])
                    new_number = last_number + 1
                except (ValueError, IndexError):
                    new_number = 1
            else:
                new_number = 1
            
            self.vendor_credit_number = f'VCREDIT-{new_number:06d}'
        
        super().save(*args, **kwargs)


class VendorCreditItem(models.Model):
    """
    Individual items in a Vendor Credit
    Following exact GRN Return Item pattern but for vendor credits
    """
    vendor_credit_item_id = models.AutoField(primary_key=True)
    vendor_credit = models.ForeignKey(VendorCredit, on_delete=models.CASCADE, related_name='items')
    product = models.ForeignKey('sales.Product', on_delete=models.CASCADE, related_name='vendor_credit_items')

    # Original GRN return item reference for traceability
    original_return_item = models.ForeignKey(
        'inventory.GoodsReturnNoteItem',
        on_delete=models.CASCADE,
        related_name='vendor_credit_items',
        help_text="Original GRN return item being credited"
    )

    # Quantities
    quantity_returned = models.DecimalField(
        max_digits=15,
        decimal_places=4,
        help_text="Quantity originally returned"
    )
    quantity_credited = models.DecimalField(
        max_digits=15,
        decimal_places=4,
        help_text="Quantity being credited"
    )

    # Cost information
    unit_cost = models.DecimalField(
        max_digits=15,
        decimal_places=4,
        help_text="Original cost per unit"
    )
    credit_value = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        help_text="Total credit value for this line item"
    )

    # Credit specific details
    credit_reason = models.CharField(
        max_length=50,
        choices=[
            ('DEFECTIVE', 'Defective/Damaged'),
            ('WRONG_ITEM', 'Wrong Item Delivered'),
            ('EXCESS_QTY', 'Excess Quantity'),
            ('QUALITY_ISSUE', 'Quality Issue'),
            ('NOT_ORDERED', 'Not Ordered'),
            ('EXPIRED', 'Expired'),
            ('OTHER', 'Other')
        ],
        help_text="Reason for this line item credit"
    )

    # Additional details
    description = models.TextField(blank=True, help_text="Additional description for this credit item")
    line_order = models.PositiveIntegerField(default=0, help_text="Order of line item")

    # Audit trail
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'vendor_credit_items'
        ordering = ['line_order', 'vendor_credit_item_id']

    def __str__(self):
        return f"{self.vendor_credit.vendor_credit_number} - {self.product.name if self.product else 'No Product'}"

    def save(self, *args, **kwargs):
        # Calculate credit value
        self.credit_value = self.quantity_credited * self.unit_cost
        super().save(*args, **kwargs)
