#!/usr/bin/env python3
"""
Test script for Vendor Credit API endpoints
"""
import requests
import json
from datetime import datetime

# API base URL
BASE_URL = "http://127.0.0.1:8000/api"

def test_vendor_credit_endpoints():
    """Test all vendor credit API endpoints"""
    
    print("🧪 Testing Vendor Credit API Endpoints")
    print("=" * 50)
    
    # Test 1: Get vendor credits list
    print("\n1. Testing GET /api/purchase/vendor-bill-credits/")
    try:
        response = requests.get(f"{BASE_URL}/purchase/vendor-bill-credits/")
        print(f"   Status Code: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Success! Found {data.get('count', 0)} vendor credits")
            print(f"   Response keys: {list(data.keys())}")
        else:
            print(f"   ❌ Error: {response.text}")
    except Exception as e:
        print(f"   ❌ Exception: {e}")
    
    # Test 2: Get vendor credit stats
    print("\n2. Testing GET /api/purchase/vendor-bill-credits/stats/")
    try:
        response = requests.get(f"{BASE_URL}/purchase/vendor-bill-credits/stats/")
        print(f"   Status Code: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Success! Stats: {data}")
        else:
            print(f"   ❌ Error: {response.text}")
    except Exception as e:
        print(f"   ❌ Exception: {e}")
    
    # Test 3: Test create credit from return note (with mock data)
    print("\n3. Testing POST /api/purchase/vendor-bill-credits/create_from_return_note/")
    try:
        # First, let's see if we have any goods return notes
        # This would normally come from the inventory API
        test_data = {
            "return_note_id": 1,  # Mock return note ID
            "credit_type": "goods_return",
            "reason": "Test credit for defective products",
            "notes": "API test credit"
        }
        
        response = requests.post(
            f"{BASE_URL}/purchase/vendor-bill-credits/create_from_return_note/",
            json=test_data,
            headers={'Content-Type': 'application/json'}
        )
        print(f"   Status Code: {response.status_code}")
        if response.status_code == 201:
            data = response.json()
            print(f"   ✅ Success! Created credit: {data.get('credit_number')}")
            return data.get('id')  # Return the created credit ID for further tests
        else:
            print(f"   ❌ Error: {response.text}")
            return None
    except Exception as e:
        print(f"   ❌ Exception: {e}")
        return None
    
    # Test 4: Test manual credit creation
    print("\n4. Testing POST /api/purchase/vendor-bill-credits/ (manual creation)")
    try:
        # Get a vendor ID first
        vendors_response = requests.get(f"{BASE_URL}/purchase/vendors/")
        if vendors_response.status_code == 200:
            vendors = vendors_response.json().get('results', [])
            if vendors:
                vendor_id = vendors[0]['id']
                
                test_data = {
                    "vendor": vendor_id,
                    "goods_return_note": 1,  # Mock return note ID
                    "credit_amount": 1000.00,
                    "tax_amount": 100.00,
                    "total_credit": 1100.00,
                    "credit_type": "goods_return",
                    "credit_date": datetime.now().strftime("%Y-%m-%d"),
                    "reason": "Manual test credit creation",
                    "notes": "Created via API test",
                    "line_items": []
                }
                
                response = requests.post(
                    f"{BASE_URL}/purchase/vendor-bill-credits/",
                    json=test_data,
                    headers={'Content-Type': 'application/json'}
                )
                print(f"   Status Code: {response.status_code}")
                if response.status_code == 201:
                    data = response.json()
                    print(f"   ✅ Success! Created credit: {data.get('credit_number')}")
                    return data.get('id')
                else:
                    print(f"   ❌ Error: {response.text}")
            else:
                print("   ⚠️  No vendors found for testing")
        else:
            print(f"   ❌ Failed to get vendors: {vendors_response.text}")
    except Exception as e:
        print(f"   ❌ Exception: {e}")
    
    return None

def test_credit_workflow(credit_id):
    """Test the credit approval and application workflow"""
    if not credit_id:
        print("\n⚠️  Skipping workflow tests - no credit ID available")
        return
    
    print(f"\n🔄 Testing Credit Workflow for Credit ID: {credit_id}")
    print("=" * 50)
    
    # Test 5: Get specific credit
    print(f"\n5. Testing GET /api/purchase/vendor-bill-credits/{credit_id}/")
    try:
        response = requests.get(f"{BASE_URL}/purchase/vendor-bill-credits/{credit_id}/")
        print(f"   Status Code: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Success! Credit: {data.get('credit_number')} - Status: {data.get('status')}")
        else:
            print(f"   ❌ Error: {response.text}")
    except Exception as e:
        print(f"   ❌ Exception: {e}")
    
    # Test 6: Approve credit
    print(f"\n6. Testing POST /api/purchase/vendor-bill-credits/{credit_id}/approve_credit/")
    try:
        response = requests.post(f"{BASE_URL}/purchase/vendor-bill-credits/{credit_id}/approve_credit/")
        print(f"   Status Code: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Success! Credit approved - Status: {data.get('status')}")
        else:
            print(f"   ❌ Error: {response.text}")
    except Exception as e:
        print(f"   ❌ Exception: {e}")
    
    # Test 7: Apply credit
    print(f"\n7. Testing POST /api/purchase/vendor-bill-credits/{credit_id}/apply_credit/")
    try:
        response = requests.post(f"{BASE_URL}/purchase/vendor-bill-credits/{credit_id}/apply_credit/")
        print(f"   Status Code: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Success! Credit applied - Status: {data.get('status')}")
        else:
            print(f"   ❌ Error: {response.text}")
    except Exception as e:
        print(f"   ❌ Exception: {e}")

def test_related_endpoints():
    """Test related endpoints that vendor credits depend on"""
    print("\n🔗 Testing Related Endpoints")
    print("=" * 50)
    
    # Test vendors endpoint
    print("\n8. Testing GET /api/purchase/vendors/")
    try:
        response = requests.get(f"{BASE_URL}/purchase/vendors/")
        print(f"   Status Code: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Success! Found {data.get('count', 0)} vendors")
        else:
            print(f"   ❌ Error: {response.text}")
    except Exception as e:
        print(f"   ❌ Exception: {e}")
    
    # Test vendor bills endpoint
    print("\n9. Testing GET /api/purchase/vendor-bills/")
    try:
        response = requests.get(f"{BASE_URL}/purchase/vendor-bills/")
        print(f"   Status Code: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Success! Found {data.get('count', 0)} vendor bills")
        else:
            print(f"   ❌ Error: {response.text}")
    except Exception as e:
        print(f"   ❌ Exception: {e}")

if __name__ == "__main__":
    print("🚀 Starting Vendor Credit API Tests")
    print("Make sure the Django server is running on http://127.0.0.1:8000")
    print()
    
    # Test basic endpoints
    credit_id = test_vendor_credit_endpoints()
    
    # Test workflow if we created a credit
    test_credit_workflow(credit_id)
    
    # Test related endpoints
    test_related_endpoints()
    
    print("\n🎉 API Testing Complete!")
    print("Check the results above to see which endpoints are working correctly.")
