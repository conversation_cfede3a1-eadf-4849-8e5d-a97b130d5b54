import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import {
  Box,
  Button,
  Card,
  CardContent,
  FormControl,
  Grid,
  InputLabel,
  MenuItem,
  Paper,
  Select,
  TextField,
  Typography,
  Alert,
  CircularProgress
} from '@mui/material';
import { ArrowBack as ArrowBackIcon } from '@mui/icons-material';
import { CreateCreditFromReturnData, CREDIT_TYPE_CHOICES } from '../../../types/vendorCredit';
import vendorCreditService from '../../../services/vendorCredit.service';
import LoadingSpinner from '../../../shared/components/LoadingSpinner';

interface GoodsReturnNote {
  grn_return_id: number;
  grn_return_number: string;
  vendor_name: string;
  return_date: string;
  return_reason: string;
  total_return_value: number;
  status: string;
}

const CreateVendorCreditPage: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const returnNoteId = searchParams.get('return_note_id');

  const [formData, setFormData] = useState<CreateCreditFromReturnData>({
    return_note_id: returnNoteId ? parseInt(returnNoteId) : 0,
    credit_type: 'goods_return',
    reason: '',
    notes: '',
  });

  const [returnNote, setReturnNote] = useState<GoodsReturnNote | null>(null);
  const [availableReturnNotes, setAvailableReturnNotes] = useState<GoodsReturnNote[]>([]);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchAvailableReturnNotes();
    if (returnNoteId) {
      fetchReturnNoteDetails(parseInt(returnNoteId));
    }
  }, [returnNoteId]);

  const fetchAvailableReturnNotes = async () => {
    try {
      setLoading(true);
      // This would be a call to get return notes that don't have credits yet
      // For now, we'll simulate this
      const mockReturnNotes: GoodsReturnNote[] = [
        {
          grn_return_id: 1,
          grn_return_number: 'GRN-RET-000001',
          vendor_name: 'ABC Suppliers',
          return_date: '2024-01-15',
          return_reason: 'Defective products',
          total_return_value: 1250.00,
          status: 'approved'
        },
        {
          grn_return_id: 2,
          grn_return_number: 'GRN-RET-000002',
          vendor_name: 'XYZ Materials',
          return_date: '2024-01-14',
          return_reason: 'Wrong items delivered',
          total_return_value: 850.00,
          status: 'approved'
        }
      ];
      setAvailableReturnNotes(mockReturnNotes);
    } catch (err) {
      console.error('Error fetching return notes:', err);
      setError('Failed to fetch available return notes');
    } finally {
      setLoading(false);
    }
  };

  const fetchReturnNoteDetails = async (id: number) => {
    try {
      const note = availableReturnNotes.find(n => n.grn_return_id === id);
      if (note) {
        setReturnNote(note);
        setFormData(prev => ({
          ...prev,
          return_note_id: id,
          reason: `Credit for goods return: ${note.return_reason}`,
        }));
      }
    } catch (err) {
      console.error('Error fetching return note details:', err);
    }
  };

  const handleInputChange = (field: keyof CreateCreditFromReturnData, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    if (field === 'return_note_id' && typeof value === 'number') {
      const selectedNote = availableReturnNotes.find(n => n.grn_return_id === value);
      if (selectedNote) {
        setReturnNote(selectedNote);
        setFormData(prev => ({
          ...prev,
          reason: `Credit for goods return: ${selectedNote.return_reason}`,
        }));
      }
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.return_note_id || !formData.reason.trim()) {
      setError('Please fill in all required fields');
      return;
    }

    try {
      setSubmitting(true);
      setError(null);
      
      const credit = await vendorCreditService.createCreditFromReturnNote(formData);
      navigate(`/dashboard/purchase/vendor-credits/${credit.id}`);
    } catch (err: any) {
      console.error('Error creating credit:', err);
      setError(err.response?.data?.error || 'Failed to create vendor credit');
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return <LoadingSpinner />;
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box display="flex" alignItems="center" gap={2} mb={3}>
        <Button
          startIcon={<ArrowBackIcon />}
          onClick={() => navigate('/dashboard/purchase/vendor-credits')}
          color="inherit"
        >
          Back to Credits
        </Button>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom>
            Create Vendor Credit
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Create a vendor credit note from a goods return note
          </Typography>
        </Box>
      </Box>

      {/* Error Message */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Form */}
      <form onSubmit={handleSubmit}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Credit Information
            </Typography>

            <Grid container spacing={3}>
              {/* Return Note Selection */}
              <Grid item xs={12}>
                <FormControl fullWidth required>
                  <InputLabel>Goods Return Note</InputLabel>
                  <Select
                    value={formData.return_note_id}
                    onChange={(e) => handleInputChange('return_note_id', parseInt(e.target.value as string))}
                    label="Goods Return Note"
                  >
                    <MenuItem value={0}>Select a return note...</MenuItem>
                    {availableReturnNotes.map((note) => (
                      <MenuItem key={note.grn_return_id} value={note.grn_return_id}>
                        {note.grn_return_number} - {note.vendor_name} - ${note.total_return_value.toFixed(2)}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              {/* Return Note Details */}
              {returnNote && (
                <Grid item xs={12}>
                  <Paper sx={{ p: 2, bgcolor: 'grey.50' }}>
                    <Typography variant="subtitle2" gutterBottom>
                      Return Note Details
                    </Typography>
                    <Grid container spacing={2}>
                      <Grid item xs={6}>
                        <Typography variant="body2" color="text.secondary">
                          Vendor: <strong>{returnNote.vendor_name}</strong>
                        </Typography>
                      </Grid>
                      <Grid item xs={6}>
                        <Typography variant="body2" color="text.secondary">
                          Return Date: <strong>{new Date(returnNote.return_date).toLocaleDateString()}</strong>
                        </Typography>
                      </Grid>
                      <Grid item xs={6}>
                        <Typography variant="body2" color="text.secondary">
                          Reason: <strong>{returnNote.return_reason}</strong>
                        </Typography>
                      </Grid>
                      <Grid item xs={6}>
                        <Typography variant="body2" color="text.secondary">
                          Total Value: <strong>${returnNote.total_return_value.toFixed(2)}</strong>
                        </Typography>
                      </Grid>
                    </Grid>
                  </Paper>
                </Grid>
              )}

              {/* Credit Type */}
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>Credit Type</InputLabel>
                  <Select
                    value={formData.credit_type}
                    onChange={(e) => handleInputChange('credit_type', e.target.value)}
                    label="Credit Type"
                  >
                    {CREDIT_TYPE_CHOICES.map((choice) => (
                      <MenuItem key={choice.value} value={choice.value}>
                        {choice.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              {/* Reason */}
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  multiline
                  rows={3}
                  label="Reason for Credit"
                  value={formData.reason}
                  onChange={(e) => handleInputChange('reason', e.target.value)}
                  placeholder="Enter the reason for this credit..."
                  required
                />
              </Grid>

              {/* Notes */}
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  multiline
                  rows={3}
                  label="Additional Notes"
                  value={formData.notes}
                  onChange={(e) => handleInputChange('notes', e.target.value)}
                  placeholder="Any additional notes or comments..."
                />
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        {/* Form Actions */}
        <Box display="flex" justifyContent="flex-end" gap={2} mt={3}>
          <Button
            variant="outlined"
            onClick={() => navigate('/dashboard/purchase/vendor-credits')}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="contained"
            disabled={submitting || !formData.return_note_id || !formData.reason.trim()}
            startIcon={submitting ? <CircularProgress size={20} /> : undefined}
          >
            {submitting ? 'Creating...' : 'Create Credit'}
          </Button>
        </Box>
      </form>
    </Box>
  );
};

export default CreateVendorCreditPage;
