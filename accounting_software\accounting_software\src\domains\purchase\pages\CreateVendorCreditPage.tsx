import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, useSearchParams } from 'react-router-dom';
import {
  Box,
  Paper,
  Alert,
  CircularProgress,
  Typography,
  Button,
  Card,
  CardContent,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
} from '@mui/material';
import { CreditCard as CreditIcon, ArrowBack as BackIcon } from '@mui/icons-material';
import { PageContainer, PageHeader } from '../../../layouts/components/PageComponents';
import { vendorCreditService, VendorCreditFormData } from '../services/vendor-credit.service';

const CreateVendorCreditPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const returnNoteId = searchParams.get('return_note_id');
  
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [submitting, setSubmitting] = useState(false);
  
  // Form data
  const [formData, setFormData] = useState<VendorCreditFormData>({
    original_grn_return: returnNoteId ? parseInt(returnNoteId) : 0,
    credit_date: new Date().toISOString().split('T')[0],
    credit_reason: 'DEFECTIVE',
    notes: '',
    items: [],
  });

  const isEditMode = Boolean(id && id !== 'new');

  useEffect(() => {
    console.log('CreateVendorCreditPage - useEffect triggered', { id, isEditMode, returnNoteId });
    if (isEditMode) {
      loadVendorCredit();
    } else if (returnNoteId) {
      console.log('Pre-populating from return note ID:', returnNoteId);
      // Pre-populate from return note
      setFormData(prev => ({
        ...prev,
        original_grn_return: parseInt(returnNoteId),
      }));
    }
  }, [id, isEditMode, returnNoteId]);

  const loadVendorCredit = async () => {
    if (!id) return;
    
    try {
      setLoading(true);
      setError(null);
      const credit = await vendorCreditService.getVendorCredit(parseInt(id));
      setFormData({
        original_grn_return: credit.original_grn_return,
        credit_date: credit.credit_date,
        credit_reason: credit.credit_reason,
        notes: credit.notes || '',
        items: credit.items || [],
      });
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load vendor credit');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof VendorCreditFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    console.log('CreateVendorCreditPage - handleSubmit called', { formData, returnNoteId, isEditMode });

    if (!formData.original_grn_return) {
      setError('Please select a goods return note');
      return;
    }

    try {
      setSubmitting(true);
      setError(null);

      if (isEditMode && id) {
        console.log('Updating existing vendor credit');
        await vendorCreditService.updateVendorCredit(parseInt(id), formData);
      } else {
        // If we have a return note ID, use the createCreditFromReturn method
        if (returnNoteId) {
          console.log('Creating credit from return note:', returnNoteId);
          await vendorCreditService.createCreditFromReturn(parseInt(returnNoteId), {
            credit_reason: formData.credit_reason,
            notes: formData.notes,
          });
        } else {
          console.log('Creating regular vendor credit');
          await vendorCreditService.createVendorCredit(formData);
        }
      }

      navigate('/dashboard/purchases/vendor-credits');
    } catch (err) {
      console.error('Error in handleSubmit:', err);
      setError(err instanceof Error ? err.message : 'Failed to save vendor credit');
    } finally {
      setSubmitting(false);
    }
  };

  const handleCancel = () => {
    navigate('/dashboard/purchases/vendor-credits');
  };

  if (loading) {
    return (
      <PageContainer>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <CircularProgress />
          <Typography sx={{ ml: 2 }}>Loading credit details...</Typography>
        </Box>
      </PageContainer>
    );
  }

  return (
    <PageContainer>
      <PageHeader
        title={isEditMode ? 'Edit Vendor Credit' : 'Create Vendor Credit'}
        action={
          <Button
            variant="outlined"
            startIcon={<BackIcon />}
            onClick={handleCancel}
          >
            Back to Credits
          </Button>
        }
      />

      {!isEditMode && (
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          Create a new vendor credit from goods return
        </Typography>
      )}

      {isEditMode && (
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          Edit credit #{id}
        </Typography>
      )}

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <form onSubmit={handleSubmit}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Credit Information
            </Typography>
            
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Goods Return Note ID"
                  value={formData.original_grn_return}
                  onChange={(e) => handleInputChange('original_grn_return', parseInt(e.target.value) || 0)}
                  type="number"
                  required
                  disabled={!!returnNoteId} // Disable if coming from return note
                  helperText={returnNoteId ? 'Pre-filled from goods return note' : 'Enter the goods return note ID'}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Credit Date"
                  type="date"
                  value={formData.credit_date}
                  onChange={(e) => handleInputChange('credit_date', e.target.value)}
                  InputLabelProps={{ shrink: true }}
                  required
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <FormControl fullWidth required>
                  <InputLabel>Credit Reason</InputLabel>
                  <Select
                    value={formData.credit_reason}
                    onChange={(e) => handleInputChange('credit_reason', e.target.value)}
                    label="Credit Reason"
                  >
                    <MenuItem value="DEFECTIVE">Defective/Damaged</MenuItem>
                    <MenuItem value="WRONG_ITEM">Wrong Item Delivered</MenuItem>
                    <MenuItem value="EXCESS_QTY">Excess Quantity</MenuItem>
                    <MenuItem value="QUALITY_ISSUE">Quality Issue</MenuItem>
                    <MenuItem value="NOT_ORDERED">Not Ordered</MenuItem>
                    <MenuItem value="EXPIRED">Expired</MenuItem>
                    <MenuItem value="OTHER">Other</MenuItem>
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Notes"
                  multiline
                  rows={4}
                  value={formData.notes}
                  onChange={(e) => handleInputChange('notes', e.target.value)}
                  placeholder="Enter any additional notes about this credit..."
                />
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        <Box sx={{ mt: 3, display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
          <Button
            variant="outlined"
            onClick={handleCancel}
            disabled={submitting}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="contained"
            disabled={submitting || !formData.original_grn_return}
            startIcon={submitting ? <CircularProgress size={20} /> : <CreditIcon />}
          >
            {submitting ? 'Saving...' : (isEditMode ? 'Update Credit' : 'Create Credit')}
          </Button>
        </Box>
      </form>
    </PageContainer>
  );
};

export default CreateVendorCreditPage;
