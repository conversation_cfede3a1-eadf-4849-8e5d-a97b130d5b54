import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { ArrowLeftIcon } from '@heroicons/react/24/outline';
import { CreateCreditFromReturnData, CREDIT_TYPE_CHOICES } from '../../../types/vendorCredit';
import vendorCreditService from '../../../services/vendorCredit.service';
import LoadingSpinner from '../../../shared/components/LoadingSpinner';

interface GoodsReturnNote {
  grn_return_id: number;
  grn_return_number: string;
  vendor_name: string;
  return_date: string;
  return_reason: string;
  total_return_value: number;
  status: string;
}

const CreateVendorCreditPage: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const returnNoteId = searchParams.get('return_note_id');

  const [formData, setFormData] = useState<CreateCreditFromReturnData>({
    return_note_id: returnNoteId ? parseInt(returnNoteId) : 0,
    credit_type: 'goods_return',
    reason: '',
    notes: '',
  });

  const [returnNote, setReturnNote] = useState<GoodsReturnNote | null>(null);
  const [availableReturnNotes, setAvailableReturnNotes] = useState<GoodsReturnNote[]>([]);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchAvailableReturnNotes();
    if (returnNoteId) {
      fetchReturnNoteDetails(parseInt(returnNoteId));
    }
  }, [returnNoteId]);

  const fetchAvailableReturnNotes = async () => {
    try {
      setLoading(true);
      // This would be a call to get return notes that don't have credits yet
      // For now, we'll simulate this
      const mockReturnNotes: GoodsReturnNote[] = [
        {
          grn_return_id: 1,
          grn_return_number: 'GRN-RET-000001',
          vendor_name: 'ABC Suppliers',
          return_date: '2024-01-15',
          return_reason: 'Defective products',
          total_return_value: 1250.00,
          status: 'approved'
        },
        {
          grn_return_id: 2,
          grn_return_number: 'GRN-RET-000002',
          vendor_name: 'XYZ Materials',
          return_date: '2024-01-14',
          return_reason: 'Wrong items delivered',
          total_return_value: 850.00,
          status: 'approved'
        }
      ];
      setAvailableReturnNotes(mockReturnNotes);
    } catch (err) {
      console.error('Error fetching return notes:', err);
      setError('Failed to fetch available return notes');
    } finally {
      setLoading(false);
    }
  };

  const fetchReturnNoteDetails = async (id: number) => {
    try {
      const note = availableReturnNotes.find(n => n.grn_return_id === id);
      if (note) {
        setReturnNote(note);
        setFormData(prev => ({
          ...prev,
          return_note_id: id,
          reason: `Credit for goods return: ${note.return_reason}`,
        }));
      }
    } catch (err) {
      console.error('Error fetching return note details:', err);
    }
  };

  const handleInputChange = (field: keyof CreateCreditFromReturnData, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    if (field === 'return_note_id' && typeof value === 'number') {
      const selectedNote = availableReturnNotes.find(n => n.grn_return_id === value);
      if (selectedNote) {
        setReturnNote(selectedNote);
        setFormData(prev => ({
          ...prev,
          reason: `Credit for goods return: ${selectedNote.return_reason}`,
        }));
      }
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.return_note_id || !formData.reason.trim()) {
      setError('Please fill in all required fields');
      return;
    }

    try {
      setSubmitting(true);
      setError(null);
      
      const credit = await vendorCreditService.createCreditFromReturnNote(formData);
      navigate(`/dashboard/purchase/vendor-credits/${credit.id}`);
    } catch (err: any) {
      console.error('Error creating credit:', err);
      setError(err.response?.data?.error || 'Failed to create vendor credit');
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return <LoadingSpinner />;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <button
          onClick={() => navigate('/dashboard/purchase/vendor-credits')}
          className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700"
        >
          <ArrowLeftIcon className="h-5 w-5 mr-1" />
          Back to Credits
        </button>
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">
            Create Vendor Credit
          </h1>
          <p className="mt-1 text-sm text-gray-600">
            Create a vendor credit note from a goods return note
          </p>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="text-sm text-red-600">{error}</div>
        </div>
      )}

      {/* Form */}
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="bg-white shadow sm:rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
              Credit Information
            </h3>
            
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
              {/* Return Note Selection */}
              <div className="sm:col-span-2">
                <label htmlFor="return_note_id" className="block text-sm font-medium text-gray-700">
                  Goods Return Note *
                </label>
                <select
                  id="return_note_id"
                  value={formData.return_note_id}
                  onChange={(e) => handleInputChange('return_note_id', parseInt(e.target.value))}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                  required
                >
                  <option value={0}>Select a return note...</option>
                  {availableReturnNotes.map((note) => (
                    <option key={note.grn_return_id} value={note.grn_return_id}>
                      {note.grn_return_number} - {note.vendor_name} - ${note.total_return_value.toFixed(2)}
                    </option>
                  ))}
                </select>
              </div>

              {/* Return Note Details */}
              {returnNote && (
                <div className="sm:col-span-2 bg-gray-50 rounded-lg p-4">
                  <h4 className="text-sm font-medium text-gray-900 mb-2">Return Note Details</h4>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-500">Vendor:</span>
                      <span className="ml-2 text-gray-900">{returnNote.vendor_name}</span>
                    </div>
                    <div>
                      <span className="text-gray-500">Return Date:</span>
                      <span className="ml-2 text-gray-900">
                        {new Date(returnNote.return_date).toLocaleDateString()}
                      </span>
                    </div>
                    <div>
                      <span className="text-gray-500">Reason:</span>
                      <span className="ml-2 text-gray-900">{returnNote.return_reason}</span>
                    </div>
                    <div>
                      <span className="text-gray-500">Total Value:</span>
                      <span className="ml-2 text-gray-900 font-medium">
                        ${returnNote.total_return_value.toFixed(2)}
                      </span>
                    </div>
                  </div>
                </div>
              )}

              {/* Credit Type */}
              <div>
                <label htmlFor="credit_type" className="block text-sm font-medium text-gray-700">
                  Credit Type
                </label>
                <select
                  id="credit_type"
                  value={formData.credit_type}
                  onChange={(e) => handleInputChange('credit_type', e.target.value)}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                >
                  {CREDIT_TYPE_CHOICES.map((choice) => (
                    <option key={choice.value} value={choice.value}>
                      {choice.label}
                    </option>
                  ))}
                </select>
              </div>

              {/* Reason */}
              <div className="sm:col-span-2">
                <label htmlFor="reason" className="block text-sm font-medium text-gray-700">
                  Reason for Credit *
                </label>
                <textarea
                  id="reason"
                  rows={3}
                  value={formData.reason}
                  onChange={(e) => handleInputChange('reason', e.target.value)}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                  placeholder="Enter the reason for this credit..."
                  required
                />
              </div>

              {/* Notes */}
              <div className="sm:col-span-2">
                <label htmlFor="notes" className="block text-sm font-medium text-gray-700">
                  Additional Notes
                </label>
                <textarea
                  id="notes"
                  rows={3}
                  value={formData.notes}
                  onChange={(e) => handleInputChange('notes', e.target.value)}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                  placeholder="Any additional notes or comments..."
                />
              </div>
            </div>
          </div>
        </div>

        {/* Form Actions */}
        <div className="flex justify-end space-x-3">
          <button
            type="button"
            onClick={() => navigate('/dashboard/purchase/vendor-credits')}
            className="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={submitting || !formData.return_note_id || !formData.reason.trim()}
            className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {submitting ? (
              <>
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Creating...
              </>
            ) : (
              'Create Credit'
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default CreateVendorCreditPage;
