from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON><PERSON><PERSON>
from . import views

# Create a router and register our viewsets with it
router = DefaultRouter()
router.register(r'vendors', views.VendorViewSet, basename='vendor')
router.register(r'vendor-bills', views.VendorBillViewSet, basename='vendor-bill')
router.register(r'vendor-credits', views.VendorCreditViewSet, basename='vendor-credit')
router.register(r'purchase-orders', views.PurchaseOrderViewSet, basename='purchase-order')


# The API URLs are now determined automatically by the router
urlpatterns = [
    path('', include(router.urls)),
] 