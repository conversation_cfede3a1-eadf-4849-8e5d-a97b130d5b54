# Generated by Django 4.2.21 on 2025-07-10 09:00

import datetime
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('sales', '0012_remove_invoice_estimate_salesorder_deliverynote_payment'),
        ('inventory', '0006_add_enterprise_inventory_models'),
        ('contacts', '0006_remove_pricingrule_customer_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('purchase', '0012_add_vendor_bill_credit'),
    ]

    operations = [
        migrations.CreateModel(
            name='VendorCredit',
            fields=[
                ('vendor_credit_id', models.AutoField(primary_key=True, serialize=False)),
                ('vendor_credit_number', models.CharField(help_text='Vendor Credit reference number', max_length=50, unique=True)),
                ('credit_date', models.DateField(default=datetime.date.today)),
                ('credit_reason', models.CharField(choices=[('DEFECTIVE', 'Defective/Damaged'), ('WRONG_ITEM', 'Wrong Item Delivered'), ('EXCESS_QTY', 'Excess Quantity'), ('QUALITY_ISSUE', 'Quality Issue'), ('NOT_ORDERED', 'Not Ordered'), ('EXPIRED', 'Expired'), ('OTHER', 'Other')], help_text='Reason for credit', max_length=50)),
                ('status', models.CharField(choices=[('DRAFT', 'Draft'), ('APPROVED', 'Approved'), ('APPLIED', 'Applied to Bills'), ('POSTED', 'Posted to GL'), ('CANCELLED', 'Cancelled')], default='DRAFT', max_length=20)),
                ('notes', models.TextField(blank=True, help_text='Credit notes and comments')),
                ('total_quantity', models.DecimalField(decimal_places=4, default=Decimal('0.0000'), max_digits=15)),
                ('total_credit_amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=15)),
                ('applied_amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Amount applied to vendor bills', max_digits=15)),
                ('remaining_credit', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Remaining credit available', max_digits=15)),
                ('credit_applied_date', models.DateField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('approved_at', models.DateTimeField(blank=True, null=True)),
                ('posted_at', models.DateTimeField(blank=True, null=True)),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='vendor_credits_approved', to=settings.AUTH_USER_MODEL)),
                ('original_grn_return', models.ForeignKey(help_text='Original GRN Return that generated this credit', on_delete=django.db.models.deletion.CASCADE, related_name='vendor_credits', to='inventory.goodsreturnnote')),
                ('posted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='vendor_credits_posted', to=settings.AUTH_USER_MODEL)),
                ('vendor', models.ForeignKey(blank=True, help_text='Vendor providing the credit', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='vendor_credits', to='contacts.contact')),
            ],
            options={
                'db_table': 'vendor_credits',
                'ordering': ['-credit_date', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='VendorCreditItem',
            fields=[
                ('vendor_credit_item_id', models.AutoField(primary_key=True, serialize=False)),
                ('quantity_returned', models.DecimalField(decimal_places=4, help_text='Quantity originally returned', max_digits=15)),
                ('quantity_credited', models.DecimalField(decimal_places=4, help_text='Quantity being credited', max_digits=15)),
                ('unit_cost', models.DecimalField(decimal_places=4, help_text='Original cost per unit', max_digits=15)),
                ('credit_value', models.DecimalField(decimal_places=2, help_text='Total credit value for this line item', max_digits=15)),
                ('credit_reason', models.CharField(choices=[('DEFECTIVE', 'Defective/Damaged'), ('WRONG_ITEM', 'Wrong Item Delivered'), ('EXCESS_QTY', 'Excess Quantity'), ('QUALITY_ISSUE', 'Quality Issue'), ('NOT_ORDERED', 'Not Ordered'), ('EXPIRED', 'Expired'), ('OTHER', 'Other')], help_text='Reason for this line item credit', max_length=50)),
                ('description', models.TextField(blank=True, help_text='Additional description for this credit item')),
                ('line_order', models.PositiveIntegerField(default=0, help_text='Order of line item')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('original_return_item', models.ForeignKey(help_text='Original GRN return item being credited', on_delete=django.db.models.deletion.CASCADE, related_name='vendor_credit_items', to='inventory.goodsreturnnoteitem')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='vendor_credit_items', to='sales.product')),
                ('vendor_credit', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='purchase.vendorcredit')),
            ],
            options={
                'db_table': 'vendor_credit_items',
                'ordering': ['line_order', 'vendor_credit_item_id'],
            },
        ),
        migrations.RemoveField(
            model_name='vendorbillcredit',
            name='applied_by',
        ),
        migrations.RemoveField(
            model_name='vendorbillcredit',
            name='approved_by',
        ),
        migrations.RemoveField(
            model_name='vendorbillcredit',
            name='created_by',
        ),
        migrations.RemoveField(
            model_name='vendorbillcredit',
            name='goods_return_note',
        ),
        migrations.RemoveField(
            model_name='vendorbillcredit',
            name='original_vendor_bill',
        ),
        migrations.RemoveField(
            model_name='vendorbillcredit',
            name='vendor',
        ),
        migrations.RemoveField(
            model_name='vendorbillcredititem',
            name='credit',
        ),
        migrations.RemoveField(
            model_name='vendorbillcredititem',
            name='credit_account',
        ),
        migrations.RemoveField(
            model_name='vendorbillcredititem',
            name='product',
        ),
        migrations.RemoveField(
            model_name='vendorbillcredititem',
            name='return_item',
        ),
        migrations.AlterModelOptions(
            name='purchaseorder',
            options={'ordering': ['-order_date', '-created_at']},
        ),
        migrations.AlterModelOptions(
            name='purchaseorderlineitem',
            options={'ordering': ['line_order', 'id']},
        ),
        migrations.AlterModelOptions(
            name='vendorbill',
            options={'ordering': ['-bill_date', '-created_at']},
        ),
        migrations.AlterModelOptions(
            name='vendorbillitem',
            options={'ordering': ['line_order', 'id']},
        ),
        migrations.RenameField(
            model_name='purchaseorder',
            old_name='expected_date',
            new_name='expected_delivery_date',
        ),
        migrations.RemoveField(
            model_name='purchaseorder',
            name='acknowledged_date',
        ),
        migrations.RemoveField(
            model_name='purchaseorder',
            name='amount_received',
        ),
        migrations.RemoveField(
            model_name='purchaseorder',
            name='balance_due',
        ),
        migrations.RemoveField(
            model_name='purchaseorder',
            name='buyer_email',
        ),
        migrations.RemoveField(
            model_name='purchaseorder',
            name='buyer_name',
        ),
        migrations.RemoveField(
            model_name='purchaseorder',
            name='buyer_phone',
        ),
        migrations.RemoveField(
            model_name='purchaseorder',
            name='discount_amount',
        ),
        migrations.RemoveField(
            model_name='purchaseorder',
            name='discount_percent',
        ),
        migrations.RemoveField(
            model_name='purchaseorder',
            name='email_sent',
        ),
        migrations.RemoveField(
            model_name='purchaseorder',
            name='email_sent_date',
        ),
        migrations.RemoveField(
            model_name='purchaseorder',
            name='memo',
        ),
        migrations.RemoveField(
            model_name='purchaseorder',
            name='payment_terms',
        ),
        migrations.RemoveField(
            model_name='purchaseorder',
            name='po_date',
        ),
        migrations.RemoveField(
            model_name='purchaseorder',
            name='po_id',
        ),
        migrations.RemoveField(
            model_name='purchaseorder',
            name='reference_number',
        ),
        migrations.RemoveField(
            model_name='purchaseorder',
            name='ship_to_address',
        ),
        migrations.RemoveField(
            model_name='purchaseorderlineitem',
            name='description',
        ),
        migrations.RemoveField(
            model_name='purchaseorderlineitem',
            name='discount_percent',
        ),
        migrations.RemoveField(
            model_name='purchaseorderlineitem',
            name='quantity_pending',
        ),
        migrations.RemoveField(
            model_name='purchaseorderlineitem',
            name='quantity_received',
        ),
        migrations.RemoveField(
            model_name='purchaseorderlineitem',
            name='tax_amount',
        ),
        migrations.RemoveField(
            model_name='purchaseorderlineitem',
            name='tax_rate',
        ),
        migrations.RemoveField(
            model_name='purchaseorderlineitem',
            name='taxable',
        ),
        migrations.RemoveField(
            model_name='purchaseorderlineitem',
            name='unit_of_measure',
        ),
        migrations.RemoveField(
            model_name='vendorbill',
            name='bill_id',
        ),
        migrations.RemoveField(
            model_name='vendorbill',
            name='goods_return_note',
        ),
        migrations.RemoveField(
            model_name='vendorbill',
            name='payment_terms',
        ),
        migrations.RemoveField(
            model_name='vendorbillitem',
            name='tax_amount',
        ),
        migrations.RemoveField(
            model_name='vendorbillitem',
            name='tax_rate',
        ),
        migrations.AddField(
            model_name='purchaseorder',
            name='order_date',
            field=models.DateField(default=django.utils.timezone.now),
        ),
        migrations.AddField(
            model_name='purchaseorder',
            name='terms_and_conditions',
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name='purchaseorderlineitem',
            name='item_description',
            field=models.CharField(default='Default item description', help_text='Description of the item', max_length=255),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='vendorbillitem',
            name='notes',
            field=models.TextField(blank=True),
        ),
        migrations.AlterField(
            model_name='purchaseorder',
            name='notes',
            field=models.TextField(blank=True, default='', help_text='Order notes and comments'),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='purchaseorder',
            name='po_number',
            field=models.CharField(help_text='Purchase Order Number', max_length=50, unique=True),
        ),
        migrations.AlterField(
            model_name='purchaseorder',
            name='status',
            field=models.CharField(choices=[('draft', 'Draft'), ('sent', 'Sent'), ('confirmed', 'Confirmed'), ('received', 'Received'), ('cancelled', 'Cancelled')], default='draft', max_length=20),
        ),
        migrations.AlterField(
            model_name='purchaseorder',
            name='vendor',
            field=models.ForeignKey(help_text='Vendor for this purchase order', on_delete=django.db.models.deletion.CASCADE, related_name='purchase_orders', to='contacts.contact'),
        ),
        migrations.AlterField(
            model_name='purchaseorderlineitem',
            name='line_order',
            field=models.PositiveIntegerField(default=0, help_text='Order of line item'),
        ),
        migrations.AlterField(
            model_name='purchaseorderlineitem',
            name='line_total',
            field=models.DecimalField(decimal_places=2, help_text='Line total amount', max_digits=15),
        ),
        migrations.AlterField(
            model_name='purchaseorderlineitem',
            name='notes',
            field=models.TextField(blank=True),
        ),
        migrations.AlterField(
            model_name='purchaseorderlineitem',
            name='product',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='purchase_order_items', to='sales.product'),
        ),
        migrations.AlterField(
            model_name='purchaseorderlineitem',
            name='quantity',
            field=models.DecimalField(decimal_places=4, help_text='Quantity ordered', max_digits=15),
        ),
        migrations.AlterField(
            model_name='purchaseorderlineitem',
            name='unit_price',
            field=models.DecimalField(decimal_places=2, help_text='Unit price', max_digits=15),
        ),
        migrations.AlterField(
            model_name='vendorbill',
            name='bill_date',
            field=models.DateField(default=django.utils.timezone.now),
        ),
        migrations.AlterField(
            model_name='vendorbill',
            name='bill_number',
            field=models.CharField(help_text='Bill Number', max_length=50, unique=True),
        ),
        migrations.AlterField(
            model_name='vendorbill',
            name='due_date',
            field=models.DateField(),
        ),
        migrations.AlterField(
            model_name='vendorbill',
            name='grn',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='vendor_bills', to='inventory.goodsreceiptnote'),
        ),
        migrations.AlterField(
            model_name='vendorbill',
            name='notes',
            field=models.TextField(blank=True, help_text='Bill notes and comments'),
        ),
        migrations.AlterField(
            model_name='vendorbill',
            name='purchase_order',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='vendor_bills', to='purchase.purchaseorder'),
        ),
        migrations.AlterField(
            model_name='vendorbill',
            name='reference_number',
            field=models.CharField(blank=True, help_text="Vendor's reference number", max_length=100),
        ),
        migrations.AlterField(
            model_name='vendorbill',
            name='status',
            field=models.CharField(choices=[('draft', 'Draft'), ('approved', 'Approved'), ('paid', 'Paid'), ('cancelled', 'Cancelled')], default='draft', max_length=20),
        ),
        migrations.AlterField(
            model_name='vendorbill',
            name='subtotal',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=15),
        ),
        migrations.AlterField(
            model_name='vendorbill',
            name='tax_amount',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=15),
        ),
        migrations.AlterField(
            model_name='vendorbill',
            name='total_amount',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=15),
        ),
        migrations.AlterField(
            model_name='vendorbill',
            name='vendor',
            field=models.ForeignKey(help_text='Vendor for this bill', on_delete=django.db.models.deletion.CASCADE, related_name='vendor_bills', to='contacts.contact'),
        ),
        migrations.AlterField(
            model_name='vendorbillitem',
            name='account_code',
            field=models.CharField(blank=True, help_text='GL account code', max_length=20),
        ),
        migrations.AlterField(
            model_name='vendorbillitem',
            name='item_description',
            field=models.CharField(help_text='Description of the item', max_length=255),
        ),
        migrations.AlterField(
            model_name='vendorbillitem',
            name='line_order',
            field=models.PositiveIntegerField(default=0, help_text='Order of line item'),
        ),
        migrations.AlterField(
            model_name='vendorbillitem',
            name='line_total',
            field=models.DecimalField(decimal_places=2, help_text='Line total amount', max_digits=15),
        ),
        migrations.AlterField(
            model_name='vendorbillitem',
            name='product',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='vendor_bill_items', to='sales.product'),
        ),
        migrations.AlterField(
            model_name='vendorbillitem',
            name='quantity',
            field=models.DecimalField(decimal_places=4, help_text='Quantity billed', max_digits=15),
        ),
        migrations.AlterField(
            model_name='vendorbillitem',
            name='unit_price',
            field=models.DecimalField(decimal_places=2, help_text='Unit price', max_digits=15),
        ),
        migrations.AlterModelTable(
            name='vendorbill',
            table='vendor_bills',
        ),
        migrations.AlterModelTable(
            name='vendorbillitem',
            table='vendor_bill_line_items',
        ),
        migrations.DeleteModel(
            name='PaymentTerm',
        ),
        migrations.DeleteModel(
            name='VendorBillCredit',
        ),
        migrations.DeleteModel(
            name='VendorBillCreditItem',
        ),
    ]
