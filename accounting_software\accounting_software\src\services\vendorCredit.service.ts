import api from './api';
import {
  VendorCredit,
  CreateVendorCreditData,
  CreateCreditFromReturnData,
  VendorCreditFilters,
  VendorCreditStats,
  PaginatedVendorCredits
} from '../types/vendorCredit';

class VendorCreditService {
  private baseURL = '/purchase/vendor-bill-credits';

  // Get all vendor credits with optional filters
  async getVendorCredits(filters?: VendorCreditFilters): Promise<PaginatedVendorCredits> {
    try {
      const params = new URLSearchParams();
      
      if (filters) {
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== '') {
            params.append(key, value.toString());
          }
        });
      }

      const response = await api.get(`${this.baseURL}/?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching vendor credits:', error);
      throw error;
    }
  }

  // Get a specific vendor credit by ID
  async getVendorCredit(id: number): Promise<VendorCredit> {
    try {
      const response = await api.get(`${this.baseURL}/${id}/`);
      return response.data;
    } catch (error) {
      console.error('Error fetching vendor credit:', error);
      throw error;
    }
  }

  // Create a new vendor credit
  async createVendorCredit(data: CreateVendorCreditData): Promise<VendorCredit> {
    try {
      const response = await api.post(`${this.baseURL}/`, data);
      return response.data;
    } catch (error) {
      console.error('Error creating vendor credit:', error);
      throw error;
    }
  }

  // Create vendor credit from goods return note
  async createCreditFromReturnNote(data: CreateCreditFromReturnData): Promise<VendorCredit> {
    try {
      const response = await api.post(`${this.baseURL}/create_from_return_note/`, data);
      return response.data;
    } catch (error) {
      console.error('Error creating credit from return note:', error);
      throw error;
    }
  }

  // Update a vendor credit
  async updateVendorCredit(id: number, data: Partial<CreateVendorCreditData>): Promise<VendorCredit> {
    try {
      const response = await api.patch(`${this.baseURL}/${id}/`, data);
      return response.data;
    } catch (error) {
      console.error('Error updating vendor credit:', error);
      throw error;
    }
  }

  // Delete a vendor credit
  async deleteVendorCredit(id: number): Promise<void> {
    try {
      await api.delete(`${this.baseURL}/${id}/`);
    } catch (error) {
      console.error('Error deleting vendor credit:', error);
      throw error;
    }
  }

  // Approve a vendor credit
  async approveCredit(id: number): Promise<VendorCredit> {
    try {
      const response = await api.post(`${this.baseURL}/${id}/approve_credit/`);
      return response.data;
    } catch (error) {
      console.error('Error approving vendor credit:', error);
      throw error;
    }
  }

  // Apply a vendor credit
  async applyCredit(id: number): Promise<VendorCredit> {
    try {
      const response = await api.post(`${this.baseURL}/${id}/apply_credit/`);
      return response.data;
    } catch (error) {
      console.error('Error applying vendor credit:', error);
      throw error;
    }
  }

  // Get vendor credit statistics
  async getVendorCreditStats(): Promise<VendorCreditStats> {
    try {
      const response = await api.get(`${this.baseURL}/stats/`);
      return response.data;
    } catch (error) {
      console.error('Error fetching vendor credit stats:', error);
      throw error;
    }
  }

  // Get credits for a specific vendor
  async getVendorCreditsForVendor(vendorId: number): Promise<VendorCredit[]> {
    try {
      const response = await this.getVendorCredits({ vendor: vendorId });
      return response.results;
    } catch (error) {
      console.error('Error fetching vendor credits for vendor:', error);
      throw error;
    }
  }

  // Get credits for a specific goods return note
  async getCreditsForReturnNote(returnNoteId: number): Promise<VendorCredit[]> {
    try {
      const response = await this.getVendorCredits({ goods_return_note: returnNoteId });
      return response.results;
    } catch (error) {
      console.error('Error fetching credits for return note:', error);
      throw error;
    }
  }

  // Search vendor credits
  async searchVendorCredits(query: string): Promise<PaginatedVendorCredits> {
    try {
      return await this.getVendorCredits({ search: query });
    } catch (error) {
      console.error('Error searching vendor credits:', error);
      throw error;
    }
  }

  // Get credits by status
  async getCreditsByStatus(status: string): Promise<VendorCredit[]> {
    try {
      const response = await this.getVendorCredits({ status });
      return response.results;
    } catch (error) {
      console.error('Error fetching credits by status:', error);
      throw error;
    }
  }

  // Get credits by date range
  async getCreditsByDateRange(startDate: string, endDate: string): Promise<VendorCredit[]> {
    try {
      const response = await this.getVendorCredits({ 
        start_date: startDate, 
        end_date: endDate 
      });
      return response.results;
    } catch (error) {
      console.error('Error fetching credits by date range:', error);
      throw error;
    }
  }

  // Format currency for display
  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
    }).format(amount);
  }

  // Format date for display
  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  }

  // Calculate remaining credit percentage
  calculateRemainingPercentage(credit: VendorCredit): number {
    if (credit.total_credit === 0) return 0;
    return (credit.remaining_credit / credit.total_credit) * 100;
  }
}

export default new VendorCreditService();
