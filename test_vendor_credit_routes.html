<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vendor Credit Routes Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .route { margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
        .route a { text-decoration: none; color: #007bff; font-weight: bold; }
        .route a:hover { text-decoration: underline; }
        .description { color: #666; font-size: 14px; margin-top: 5px; }
    </style>
</head>
<body>
    <h1>🎯 Vendor Credit Routes - Quick Access</h1>
    <p>Click on any link below to test the vendor credit pages:</p>
    
    <div class="route">
        <a href="http://localhost:3000/dashboard/purchase" target="_blank">
            📋 Purchase Module Dashboard
        </a>
        <div class="description">Main purchase module page - should show "Vendor Credits" in sidebar</div>
    </div>
    
    <div class="route">
        <a href="http://localhost:3000/dashboard/purchase/vendor-credits" target="_blank">
            💳 Vendor Credits List
        </a>
        <div class="description">Main vendor credits page with list, search, and filters</div>
    </div>
    
    <div class="route">
        <a href="http://localhost:3000/dashboard/purchase/vendor-credits/new" target="_blank">
            ➕ Create New Vendor Credit
        </a>
        <div class="description">Create a new vendor credit from goods return note</div>
    </div>
    
    <div class="route">
        <a href="http://localhost:3000/dashboard/inventory/grn-returns" target="_blank">
            📦 GRN Returns (Integration Point)
        </a>
        <div class="description">View goods return notes - should have "Create Credit" buttons</div>
    </div>
    
    <h2>🔧 Troubleshooting</h2>
    <ul>
        <li><strong>If links don't work:</strong> Make sure frontend is running on port 3000</li>
        <li><strong>If "Vendor Credits" not in sidebar:</strong> Clear browser cache and refresh</li>
        <li><strong>If pages show errors:</strong> Check browser console for compilation errors</li>
        <li><strong>If backend errors:</strong> Make sure Django server is running on port 8000</li>
    </ul>
    
    <h2>📁 File Locations</h2>
    <ul>
        <li><code>src/domains/purchase/pages/VendorCreditsPage.tsx</code> - Main list page</li>
        <li><code>src/domains/purchase/pages/CreateVendorCreditPage.tsx</code> - Create page</li>
        <li><code>src/domains/purchase/pages/VendorCreditDetailPage.tsx</code> - Detail page</li>
        <li><code>src/domains/purchase/config/purchaseMenuConfig.ts</code> - Menu configuration</li>
    </ul>
    
    <h2>🚀 Quick Start Commands</h2>
    <pre>
# Start Backend
cd accounting_software/accounting_software/erp_backend
python manage.py runserver

# Start Frontend (in new terminal)
cd accounting_software/accounting_software
npm start
    </pre>
</body>
</html>
