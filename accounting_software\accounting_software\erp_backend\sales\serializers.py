from rest_framework import serializers
from .models import (
    ProductCategory, Product, Invoice, InvoiceLineItem, Payment, 
    Estimate, EstimateLineItem, PaymentTerm, SalesOrder, SalesOrderLineItem, 
    DeliveryNote, DeliveryNoteItem
)
from contacts.models import Contact
from django.db import models


class CustomerSerializer(serializers.ModelSerializer):
    """Serializer for Customer model (now using contacts.Contact)"""
    
    class Meta:
        model = Contact
        fields = '__all__'
        read_only_fields = ('id', 'created_at', 'updated_at', 'created_by')
    
    def create(self, validated_data):
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)


class ProductCategorySerializer(serializers.ModelSerializer):
    """Enhanced serializer for ProductCategory model"""
    
    # Computed fields
    products_count = serializers.ReadOnlyField()
    subcategories_count = serializers.ReadOnlyField()
    full_path = serializers.ReadOnlyField()
    
    # Related fields
    parent_category_name = serializers.CharField(source='parent_category.name', read_only=True)
    subcategories = serializers.SerializerMethodField()
    
    class Meta:
        model = ProductCategory
        fields = [
            'id', 'name', 'code', 'description', 'parent_category', 'parent_category_name',
            'level', 'division_type', 'image_url', 'tax_category', 'margin_percentage',
            'is_active', 'sort_order', 'allow_subcategories', 'requires_expiry_tracking',
            'requires_batch_tracking', 'default_unit_of_measure', 'created_at', 'updated_at',
            'products_count', 'subcategories_count', 'full_path', 'subcategories'
        ]
        read_only_fields = ('created_at', 'updated_at', 'created_by', 'level')
    
    def get_subcategories(self, obj):
        """Get immediate subcategories"""
        if hasattr(obj, 'subcategories'):
            subcategories = obj.subcategories.filter(is_active=True).order_by('sort_order', 'name')
            return ProductCategorySerializer(subcategories, many=True, context=self.context).data
        return []
    
    def create(self, validated_data):
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)
    
    def validate_code(self, value):
        """Validate category code is unique"""
        if self.instance:
            # For updates, exclude current instance
            if ProductCategory.objects.filter(code=value).exclude(id=self.instance.id).exists():
                raise serializers.ValidationError("Category code must be unique.")
        else:
            # For creation
            if ProductCategory.objects.filter(code=value).exists():
                raise serializers.ValidationError("Category code must be unique.")
        return value.upper()
    
    def validate_parent_category(self, value):
        """Validate parent category to prevent circular references"""
        if value and self.instance:
            # Check if setting this parent would create a circular reference
            current_category = self.instance
            parent = value
            while parent:
                if parent.id == current_category.id:
                    raise serializers.ValidationError("Cannot set parent category that would create a circular reference.")
                parent = parent.parent_category
        return value
    
    def validate(self, data):
        """Additional validation"""
        # Check hierarchy level
        parent_category = data.get('parent_category')
        if parent_category and parent_category.level >= 3:
            raise serializers.ValidationError("Category hierarchy cannot exceed 3 levels.")
        
        return data


class ProductSerializer(serializers.ModelSerializer):
    """Serializer for Product model with GL Account integration"""
    category_name = serializers.CharField(source='category.name', read_only=True)
    
    # GL Account details for display
    income_account_name = serializers.CharField(source='income_account_gl.account_name', read_only=True)
    income_account_number = serializers.CharField(source='income_account_gl.account_number', read_only=True)
    expense_account_name = serializers.CharField(source='expense_account_gl.account_name', read_only=True)
    expense_account_number = serializers.CharField(source='expense_account_gl.account_number', read_only=True)
    inventory_account_name = serializers.CharField(source='inventory_asset_account_gl.account_name', read_only=True)
    inventory_account_number = serializers.CharField(source='inventory_asset_account_gl.account_number', read_only=True)
    
    # Add total inventory quantity from all warehouses
    total_quantity_on_hand = serializers.SerializerMethodField()
    
    class Meta:
        model = Product
        fields = '__all__'
        read_only_fields = ('product_id', 'created_at', 'updated_at', 'created_by', 'total_quantity_on_hand')
    
    def get_total_quantity_on_hand(self, obj):
        """Get total quantity from all warehouses"""
        from inventory.models import Inventory
        total_qty = Inventory.objects.filter(product=obj).aggregate(
            total=models.Sum('quantity_on_hand')
        )['total'] or 0
        return float(total_qty)
    
    def create(self, validated_data):
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)











# Create/Update serializers with nested line items
class InvoiceCreateUpdateSerializer(serializers.ModelSerializer):
    """Serializer for creating/updating invoices with line items"""
    line_items = InvoiceLineItemSerializer(many=True)
    
    class Meta:
        model = Invoice
        fields = '__all__'
        read_only_fields = ('invoice_id', 'invoice_number', 'balance_due', 'created_at', 'updated_at', 'created_by')
    
    def create(self, validated_data):
        line_items_data = validated_data.pop('line_items', [])
        validated_data['created_by'] = self.context['request'].user
        invoice = Invoice.objects.create(**validated_data)
        
        for line_item_data in line_items_data:
            InvoiceLineItem.objects.create(invoice=invoice, **line_item_data)
        
        # Recalculate totals
        self._calculate_invoice_totals(invoice)
        return invoice
    
    def update(self, instance, validated_data):
        line_items_data = validated_data.pop('line_items', [])
        
        # Update invoice fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        
        # Delete existing line items and create new ones
        instance.line_items.all().delete()
        for line_item_data in line_items_data:
            InvoiceLineItem.objects.create(invoice=instance, **line_item_data)
        
        # Recalculate totals
        self._calculate_invoice_totals(instance)
        return instance
    
    def _calculate_invoice_totals(self, invoice):
        """Calculate invoice totals based on line items"""
        line_items = invoice.line_items.all()
        
        subtotal = sum(item.line_total for item in line_items)
        tax_total = sum(item.tax_amount for item in line_items)
        
        invoice.subtotal = subtotal
        invoice.tax_amount = tax_total
        invoice.total_amount = subtotal + tax_total - invoice.discount_amount
        invoice.balance_due = invoice.total_amount - invoice.amount_paid
        invoice.save()


class EstimateCreateUpdateSerializer(serializers.ModelSerializer):
    """Serializer for creating/updating estimates with line items"""
    line_items = EstimateLineItemSerializer(many=True)
    
    class Meta:
        model = Estimate
        fields = '__all__'
        read_only_fields = ('estimate_id', 'estimate_number', 'created_at', 'updated_at', 'created_by')
    
    def create(self, validated_data):
        line_items_data = validated_data.pop('line_items', [])
        validated_data['created_by'] = self.context['request'].user
        estimate = Estimate.objects.create(**validated_data)
        
        for line_item_data in line_items_data:
            EstimateLineItem.objects.create(estimate=estimate, **line_item_data)
        
        # Recalculate totals
        self._calculate_estimate_totals(estimate)
        return estimate
    
    def update(self, instance, validated_data):
        line_items_data = validated_data.pop('line_items', [])
        
        # Update estimate fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        
        # Delete existing line items and create new ones
        instance.line_items.all().delete()
        for line_item_data in line_items_data:
            EstimateLineItem.objects.create(estimate=instance, **line_item_data)
        
        # Recalculate totals
        self._calculate_estimate_totals(instance)
        return instance
    
    def _calculate_estimate_totals(self, estimate):
        """Calculate estimate totals based on line items"""
        line_items = estimate.line_items.all()
        
        subtotal = sum(item.line_total for item in line_items)
        tax_total = sum(item.tax_amount for item in line_items)
        
        estimate.subtotal = subtotal
        estimate.tax_amount = tax_total
        estimate.total_amount = subtotal + tax_total - estimate.discount_amount
        estimate.save()


class PaymentTermSerializer(serializers.ModelSerializer):
    """Serializer for Payment Term model"""
    
    class Meta:
        model = PaymentTerm
        fields = '__all__'
        read_only_fields = ('created_at', 'updated_at', 'created_by')
    
    def create(self, validated_data):
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)


class SalesOrderLineItemSerializer(serializers.ModelSerializer):
    """Serializer for SalesOrderLineItem model"""
    product_name = serializers.CharField(source='product.name', read_only=True)
    product_sku = serializers.CharField(source='product.sku', read_only=True)
    
    class Meta:
        model = SalesOrderLineItem
        fields = '__all__'
        read_only_fields = ('line_total', 'tax_amount', 'quantity_pending')


class SalesOrderSerializer(serializers.ModelSerializer):
    """Serializer for SalesOrder model"""
    line_items = SalesOrderLineItemSerializer(many=True, read_only=True)
    customer_name = serializers.CharField(source='customer.name', read_only=True)
    customer_email = serializers.CharField(source='customer.email', read_only=True)
    
    class Meta:
        model = SalesOrder
        fields = '__all__'
        read_only_fields = ('so_id', 'so_number', 'balance_due', 'created_at', 'updated_at', 'created_by')
    
    def create(self, validated_data):
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)


class SalesOrderCreateUpdateSerializer(serializers.ModelSerializer):
    """Serializer for creating/updating sales orders with line items"""
    line_items = SalesOrderLineItemSerializer(many=True)
    
    class Meta:
        model = SalesOrder
        fields = '__all__'
        read_only_fields = ('so_id', 'so_number', 'balance_due', 'created_at', 'updated_at', 'created_by')
    
    def create(self, validated_data):
        line_items_data = validated_data.pop('line_items', [])
        validated_data['created_by'] = self.context['request'].user
        sales_order = SalesOrder.objects.create(**validated_data)
        
        for line_item_data in line_items_data:
            SalesOrderLineItem.objects.create(sales_order=sales_order, **line_item_data)
        
        # Recalculate totals
        self._calculate_sales_order_totals(sales_order)
        return sales_order
    
    def update(self, instance, validated_data):
        line_items_data = validated_data.pop('line_items', [])
        
        # Update sales order fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        
        # Update line items
        if line_items_data:
            # Delete existing line items
            instance.line_items.all().delete()
            
            # Create new line items
            for line_item_data in line_items_data:
                SalesOrderLineItem.objects.create(sales_order=instance, **line_item_data)
        
        # Recalculate totals
        self._calculate_sales_order_totals(instance)
        return instance
    
    def _calculate_sales_order_totals(self, sales_order):
        """Calculate and update sales order totals"""
        sales_order.calculate_totals()
        sales_order.save()


class DeliveryNoteItemSerializer(serializers.ModelSerializer):
    """Serializer for DeliveryNoteItem model"""
    product_name = serializers.CharField(source='product.name', read_only=True)
    product_sku = serializers.CharField(source='product.sku', read_only=True)
    sales_order_line_description = serializers.CharField(source='sales_order_line_item.description', read_only=True)
    
    class Meta:
        model = DeliveryNoteItem
        fields = '__all__'
        read_only_fields = ('line_total', 'tax_amount')


class DeliveryNoteSerializer(serializers.ModelSerializer):
    """Serializer for DeliveryNote model"""
    line_items = DeliveryNoteItemSerializer(many=True, read_only=True)
    customer_name = serializers.CharField(source='customer.name', read_only=True)
    sales_order_number = serializers.CharField(source='sales_order.so_number', read_only=True)
    warehouse_name = serializers.CharField(source='warehouse.name', read_only=True)
    
    class Meta:
        model = DeliveryNote
        fields = '__all__'
        read_only_fields = ('dn_id', 'dn_number', 'posted', 'posted_date', 'posted_by', 'created_at', 'updated_at', 'created_by')
    
    def create(self, validated_data):
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)


class DeliveryNoteCreateUpdateSerializer(serializers.ModelSerializer):
    """Serializer for creating/updating delivery notes with line items"""
    line_items = DeliveryNoteItemSerializer(many=True)
    
    class Meta:
        model = DeliveryNote
        fields = '__all__'
        read_only_fields = ('dn_id', 'dn_number', 'posted', 'posted_date', 'posted_by', 'created_at', 'updated_at', 'created_by')
    
    def create(self, validated_data):
        line_items_data = validated_data.pop('line_items', [])
        validated_data['created_by'] = self.context['request'].user
        delivery_note = DeliveryNote.objects.create(**validated_data)
        
        for line_item_data in line_items_data:
            DeliveryNoteItem.objects.create(delivery_note=delivery_note, **line_item_data)
        
        # Recalculate totals
        self._calculate_delivery_note_totals(delivery_note)
        return delivery_note
    
    def update(self, instance, validated_data):
        line_items_data = validated_data.pop('line_items', [])
        
        # Update delivery note fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        
        # Update line items
        if line_items_data:
            # Delete existing line items
            instance.line_items.all().delete()
            
            # Create new line items
            for line_item_data in line_items_data:
                DeliveryNoteItem.objects.create(delivery_note=instance, **line_item_data)
        
        # Recalculate totals
        self._calculate_delivery_note_totals(instance)
        return instance
    
    def _calculate_delivery_note_totals(self, delivery_note):
        """Calculate and update delivery note totals"""
        delivery_note.calculate_totals()
        delivery_note.save()


# Enhanced Product Serializer with Sales Price Authority
class ProductPricingSerializer(serializers.ModelSerializer):
    """Special serializer for product pricing updates by Sales Department"""
    margin_amount = serializers.ReadOnlyField()
    margin_percentage = serializers.ReadOnlyField()
    markup_percentage = serializers.ReadOnlyField()
    current_average_cost = serializers.SerializerMethodField()
    
    class Meta:
        model = Product
        fields = [
            'id', 'product_id', 'name', 'sku', 'unit_price', 'cost_price', 
            'minimum_selling_price', 'price_effective_date', 'price_last_updated_by',
            'price_last_updated_at', 'margin_amount', 'margin_percentage', 
            'markup_percentage', 'current_average_cost'
        ]
        read_only_fields = (
            'id', 'product_id', 'name', 'sku', 'cost_price', 'margin_amount', 
            'margin_percentage', 'markup_percentage', 'price_last_updated_by', 
            'price_last_updated_at', 'current_average_cost'
        )
    
    def get_current_average_cost(self, obj):
        """Get current weighted average cost"""
        return float(obj.get_current_average_cost())
    
    def validate_unit_price(self, value):
        """Validate sales price against minimum selling price"""
        if hasattr(self.instance, 'minimum_selling_price') and self.instance.minimum_selling_price:
            if value < self.instance.minimum_selling_price:
                raise serializers.ValidationError(
                    f"Sales price cannot be below minimum selling price of {self.instance.minimum_selling_price}"
                )
        return value
    
    def update(self, instance, validated_data):
        from django.utils import timezone
        # Track price updates
        if 'unit_price' in validated_data and instance.unit_price != validated_data['unit_price']:
            validated_data['price_last_updated_by'] = self.context['request'].user
            validated_data['price_last_updated_at'] = timezone.now()
            if not validated_data.get('price_effective_date'):
                validated_data['price_effective_date'] = timezone.now().date()
        
        return super().update(instance, validated_data)