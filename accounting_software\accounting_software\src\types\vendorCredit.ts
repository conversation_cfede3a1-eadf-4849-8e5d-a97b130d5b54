// Vendor Bill Credit Types

export interface VendorCreditItem {
  id?: number;
  product: number;
  product_name?: string;
  product_sku?: string;
  return_item: number;
  return_item_description?: string;
  quantity_credited: number;
  unit_price: number;
  line_total: number;
  tax_rate: number;
  tax_amount: number;
  credit_account?: number;
  description?: string;
  line_order: number;
}

export interface VendorCredit {
  id?: number;
  credit_id?: string;
  credit_number?: string;
  vendor: number;
  vendor_name?: string;
  original_vendor_bill?: number;
  original_bill_number?: string;
  goods_return_note: number;
  goods_return_number?: string;
  credit_amount: number;
  tax_amount: number;
  total_credit: number;
  applied_amount: number;
  remaining_credit: number;
  credit_type: 'goods_return' | 'price_adjustment' | 'damaged_goods' | 'billing_error' | 'discount' | 'other';
  credit_date: string;
  status: 'draft' | 'approved' | 'applied' | 'cancelled';
  reason: string;
  notes?: string;
  approved_date?: string;
  approved_by?: number;
  approved_by_name?: string;
  applied_date?: string;
  applied_by?: number;
  applied_by_name?: string;
  created_at?: string;
  updated_at?: string;
  created_by?: number;
  created_by_name?: string;
  line_items?: VendorCreditItem[];
}

export interface CreateVendorCreditData {
  vendor: number;
  original_vendor_bill?: number;
  goods_return_note: number;
  credit_amount: number;
  tax_amount: number;
  total_credit: number;
  credit_type: string;
  credit_date: string;
  reason: string;
  notes?: string;
  line_items: Omit<VendorCreditItem, 'id'>[];
}

export interface CreateCreditFromReturnData {
  return_note_id: number;
  credit_type?: string;
  reason?: string;
  notes?: string;
}

export interface VendorCreditFilters {
  status?: string;
  vendor?: number;
  credit_type?: string;
  goods_return_note?: number;
  start_date?: string;
  end_date?: string;
  search?: string;
  ordering?: string;
}

export interface VendorCreditStats {
  total_credits: number;
  draft_credits: number;
  approved_credits: number;
  applied_credits: number;
  total_credit_amount: number;
  applied_credit_amount: number;
}

export interface PaginatedVendorCredits {
  count: number;
  next?: string;
  previous?: string;
  results: VendorCredit[];
}

// Credit Type Options
export const CREDIT_TYPE_CHOICES = [
  { value: 'goods_return', label: 'Goods Return' },
  { value: 'price_adjustment', label: 'Price Adjustment' },
  { value: 'damaged_goods', label: 'Damaged Goods' },
  { value: 'billing_error', label: 'Billing Error' },
  { value: 'discount', label: 'Discount' },
  { value: 'other', label: 'Other' },
];

// Status Options
export const CREDIT_STATUS_CHOICES = [
  { value: 'draft', label: 'Draft' },
  { value: 'approved', label: 'Approved' },
  { value: 'applied', label: 'Applied' },
  { value: 'cancelled', label: 'Cancelled' },
];

// Status Colors for UI
export const CREDIT_STATUS_COLORS = {
  draft: 'bg-yellow-100 text-yellow-800',
  approved: 'bg-blue-100 text-blue-800',
  applied: 'bg-green-100 text-green-800',
  cancelled: 'bg-red-100 text-red-800',
};

// Credit Type Colors for UI
export const CREDIT_TYPE_COLORS = {
  goods_return: 'bg-purple-100 text-purple-800',
  price_adjustment: 'bg-blue-100 text-blue-800',
  damaged_goods: 'bg-red-100 text-red-800',
  billing_error: 'bg-orange-100 text-orange-800',
  discount: 'bg-green-100 text-green-800',
  other: 'bg-gray-100 text-gray-800',
};
