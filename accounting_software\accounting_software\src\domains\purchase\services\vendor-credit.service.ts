import api from '../../../services/api';

export interface VendorCreditItem {
  vendor_credit_item_id?: number;
  product?: number | null;
  product_name?: string;
  product_sku?: string;
  original_return_item?: number;
  original_return_number?: string;
  quantity_returned: number | string;
  quantity_credited: number | string;
  unit_cost: number | string;
  credit_value?: number | string;
  credit_reason: 'DEFECTIVE' | 'WRONG_ITEM' | 'EXCESS_QTY' | 'QUALITY_ISSUE' | 'NOT_ORDERED' | 'EXPIRED' | 'OTHER';
  description?: string;
  line_order?: number;
}

export interface VendorCredit {
  vendor_credit_id?: number;
  vendor_credit_number?: string;
  original_grn_return: number;
  original_grn_return_number?: string;
  vendor?: number;
  vendor_name?: string;
  credit_date: string;
  credit_reason: 'DEFECTIVE' | 'WRONG_ITEM' | 'EXCESS_QTY' | 'QUALITY_ISSUE' | 'NOT_ORDERED' | 'EXPIRED' | 'OTHER';
  status: 'DRAFT' | 'APPROVED' | 'APPLIED' | 'POSTED' | 'CANCELLED';
  notes?: string;
  total_quantity?: number;
  total_credit_amount?: number;
  applied_amount?: number;
  remaining_credit?: number;
  credit_applied_date?: string;
  created_at?: string;
  updated_at?: string;
  approved_at?: string;
  approved_by?: number;
  approved_by_name?: string;
  posted_at?: string;
  posted_by?: number;
  posted_by_name?: string;
  items?: VendorCreditItem[];
}

export interface VendorCreditFormData {
  original_grn_return: number;
  credit_date: string;
  credit_reason: 'DEFECTIVE' | 'WRONG_ITEM' | 'EXCESS_QTY' | 'QUALITY_ISSUE' | 'NOT_ORDERED' | 'EXPIRED' | 'OTHER';
  notes?: string;
  items: VendorCreditItem[];
}

export interface VendorCreditStats {
  total_credits: number;
  draft_credits: number;
  approved_credits: number;
  applied_credits: number;
  posted_credits: number;
  recent_credits: number;
  total_credit_amount: number;
  applied_credit_amount: number;
}

export interface CreditableReturnItem {
  return_item_id: number;
  product: number | null;
  product_name: string;
  product_sku: string;
  quantity_returned: number;
  credited_quantity: number;
  available_quantity: number;
  unit_cost: string;
  return_reason: string;
}

export interface CreditableReturn {
  grn_return_id: number;
  grn_return_number: string;
  vendor: number;
  vendor_name: string;
  return_date: string;
  return_reason: string;
  status: string;
  total_value: number;
  items: CreditableReturnItem[];
}

export interface VendorCreditResponse {
  count: number;
  next?: string;
  previous?: string;
  results: VendorCredit[];
}

class VendorCreditService {
  private baseUrl = '/purchase/vendor-credits';

  // Get all vendor credits with filtering and pagination
  async getVendorCredits(params?: {
    page?: number;
    page_size?: number;
    search?: string;
    status?: string;
    credit_reason?: string;
    vendor?: number;
    start_date?: string;
    end_date?: string;
  }): Promise<VendorCreditResponse> {
    const response = await api.get(this.baseUrl, { params });
    return response.data;
  }

  // Get a specific vendor credit by ID
  async getVendorCredit(id: number): Promise<VendorCredit> {
    const response = await api.get(`${this.baseUrl}/${id}/`);
    return response.data;
  }

  // Create a new vendor credit
  async createVendorCredit(data: VendorCreditFormData): Promise<VendorCredit> {
    const response = await api.post(this.baseUrl, data);
    return response.data;
  }

  // Update a vendor credit
  async updateVendorCredit(id: number, data: Partial<VendorCreditFormData>): Promise<VendorCredit> {
    const response = await api.patch(`${this.baseUrl}/${id}/`, data);
    return response.data;
  }

  // Delete a vendor credit
  async deleteCredit(id: number): Promise<void> {
    await api.delete(`${this.baseUrl}/${id}/`);
  }

  // Approve a vendor credit
  async approveCredit(id: number): Promise<VendorCredit> {
    const response = await api.post(`${this.baseUrl}/${id}/approve/`);
    return response.data;
  }

  // Apply a vendor credit to bills
  async applyCredit(id: number): Promise<VendorCredit> {
    const response = await api.post(`${this.baseUrl}/${id}/apply/`);
    return response.data;
  }

  // Post a vendor credit to GL
  async postCredit(id: number): Promise<VendorCredit> {
    const response = await api.post(`${this.baseUrl}/${id}/post/`);
    return response.data;
  }

  // Get vendor credit statistics
  async getVendorCreditStats(): Promise<VendorCreditStats> {
    const response = await api.get(`${this.baseUrl}/stats/`);
    return response.data;
  }

  // Get creditable returns (returns that can have credits created)
  async getCreditableReturns(): Promise<CreditableReturn[]> {
    const response = await api.get(`${this.baseUrl}/creditable-returns/`);
    return response.data;
  }

  // Create vendor credit from return note
  async createCreditFromReturn(returnId: number, data?: {
    credit_reason?: string;
    notes?: string;
  }): Promise<VendorCredit> {
    console.log('VendorCreditService - createCreditFromReturn called', { returnId, data });
    const requestData = {
      grn_return_id: returnId,
      ...data,
    };
    console.log('VendorCreditService - sending request:', requestData);
    const response = await api.post(`${this.baseUrl}/create-from-return/`, requestData);
    console.log('VendorCreditService - response received:', response.data);
    return response.data;
  }
}

export const vendorCreditService = new VendorCreditService();
export default vendorCreditService;
