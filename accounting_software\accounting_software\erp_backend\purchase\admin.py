from django.contrib import admin
from .models import (
    PurchaseOrder, PurchaseOrderLineItem, VendorBill, VendorBillItem,
    VendorCredit, VendorCreditItem
)


class PurchaseOrderLineItemInline(admin.TabularInline):
    """Inline admin for Purchase Order Line Items"""
    model = PurchaseOrderLineItem
    extra = 1
    fields = ('product', 'item_description', 'quantity', 'unit_price', 'line_total', 'notes')
    readonly_fields = ('line_total',)


@admin.register(PurchaseOrder)
class PurchaseOrderAdmin(admin.ModelAdmin):
    list_display = ['po_number', 'vendor', 'order_date', 'total_amount', 'status', 'created_at']
    list_filter = ['status', 'order_date', 'created_at']
    search_fields = ['po_number', 'vendor__name']
    readonly_fields = ['created_at', 'updated_at']
    inlines = [PurchaseOrderLineItemInline]
    date_hierarchy = 'order_date'


@admin.register(PurchaseOrderLineItem)
class PurchaseOrderLineItemAdmin(admin.ModelAdmin):
    list_display = ['purchase_order', 'product', 'item_description', 'quantity', 'unit_price', 'line_total']
    list_filter = ['purchase_order__status']
    search_fields = ['purchase_order__po_number', 'product__name', 'item_description']





class VendorCreditItemInline(admin.TabularInline):
    model = VendorCreditItem
    extra = 1
    fields = [
        'product', 'original_return_item', 'quantity_credited', 'unit_cost',
        'credit_value', 'credit_reason', 'description'
    ]
    readonly_fields = ['credit_value']


@admin.register(VendorCredit)
class VendorCreditAdmin(admin.ModelAdmin):
    list_display = [
        'vendor_credit_number', 'vendor', 'original_grn_return', 'total_credit_amount',
        'status', 'credit_date', 'created_at'
    ]
    list_filter = ['status', 'credit_reason', 'credit_date', 'created_at']
    search_fields = [
        'vendor_credit_number', 'vendor__name', 'original_grn_return__grn_return_number', 'notes'
    ]
    readonly_fields = [
        'vendor_credit_id', 'vendor_credit_number', 'remaining_credit', 'created_at', 'updated_at'
    ]
    inlines = [VendorCreditItemInline]
    date_hierarchy = 'credit_date'

    fieldsets = (
        ('Basic Information', {
            'fields': (
                'vendor_credit_id', 'vendor_credit_number', 'vendor', 'original_grn_return'
            )
        }),
        ('Financial Information', {
            'fields': (
                'total_credit_amount', 'applied_amount', 'remaining_credit'
            )
        }),
        ('Details', {
            'fields': ('credit_reason', 'credit_date', 'status', 'notes')
        }),
        ('Approval & Application', {
            'fields': (
                'approved_at', 'approved_by', 'posted_at', 'posted_by'
            )
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at')
        }),
    )





class VendorBillItemInline(admin.TabularInline):
    """Inline admin for Vendor Bill Items"""
    model = VendorBillItem
    extra = 1
    fields = ('product', 'item_description', 'quantity', 'unit_price', 'line_total', 'account_code')
    readonly_fields = ('line_total',)


@admin.register(VendorBill)
class VendorBillAdmin(admin.ModelAdmin):
    list_display = ['bill_number', 'vendor', 'bill_date', 'due_date', 'total_amount', 'status', 'purchase_order', 'grn', 'created_at']
    list_filter = ['status', 'bill_date', 'due_date', 'created_at']
    search_fields = ['bill_number', 'vendor__name', 'reference_number', 'notes']
    readonly_fields = ['created_at', 'updated_at']
    inlines = [VendorBillItemInline]

    fieldsets = (
        ('Basic Information', {
            'fields': ('bill_number', 'vendor', 'bill_date', 'due_date', 'reference_number')
        }),
        ('Document Links', {
            'fields': ('purchase_order', 'grn'),
            'description': 'Link to source documents for bill creation'
        }),
        ('Financial Information', {
            'fields': ('subtotal', 'tax_amount', 'total_amount', 'amount_paid', 'balance_due', 'total_credits', 'net_balance')
        }),
        ('Settings', {
            'fields': ('status',)
        }),
        ('Additional Information', {
            'fields': ('notes',)
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at', 'created_by')
        }),
    )


@admin.register(VendorBillItem)
class VendorBillItemAdmin(admin.ModelAdmin):
    list_display = ['vendor_bill', 'product', 'item_description', 'quantity', 'unit_price', 'line_total', 'account_code']
    list_filter = ['vendor_bill__status', 'account_code']
    search_fields = ['vendor_bill__bill_number', 'product__name', 'item_description', 'account_code']
