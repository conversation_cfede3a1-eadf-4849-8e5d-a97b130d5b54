from django.contrib import admin
from .models import (
    PurchaseOrder, PurchaseOrderLineItem, PaymentTerm, VendorBill, VendorBillItem,
    VendorBillCredit, VendorBillCreditItem
)


class PurchaseOrderLineItemInline(admin.TabularInline):
    """Inline admin for Purchase Order Line Items"""
    model = PurchaseOrderLineItem
    extra = 1
    fields = ('product', 'description', 'quantity', 'unit_of_measure', 'unit_price', 'discount_percent', 'line_total', 'taxable', 'notes')
    readonly_fields = ('line_total',)


@admin.register(PurchaseOrder)
class PurchaseOrderAdmin(admin.ModelAdmin):
    list_display = ['po_number', 'vendor', 'po_date', 'total_amount', 'status', 'created_at']
    list_filter = ['status', 'po_date', 'created_at']
    search_fields = ['po_number', 'vendor__name', 'reference_number']
    readonly_fields = ['po_id', 'po_number', 'subtotal', 'total_amount', 'balance_due', 'created_at', 'updated_at']
    inlines = [PurchaseOrderLineItemInline]
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('po_id', 'po_number', 'vendor', 'po_date', 'expected_date', 'reference_number')
        }),
        ('Buyer Information', {
            'fields': ('buyer_name', 'buyer_email', 'buyer_phone')
        }),
        ('Financial Information', {
            'fields': ('subtotal', 'discount_percent', 'discount_amount', 'tax_amount', 'total_amount', 'amount_received', 'balance_due')
        }),
        ('Settings', {
            'fields': ('status', 'payment_terms')
        }),
        ('Additional Information', {
            'fields': ('memo', 'notes', 'ship_to_address', 'email_sent', 'email_sent_date', 'acknowledged_date')
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at', 'created_by')
        }),
    )


@admin.register(PurchaseOrderLineItem)
class PurchaseOrderLineItemAdmin(admin.ModelAdmin):
    list_display = ['purchase_order', 'product', 'description', 'quantity', 'unit_of_measure', 'unit_price', 'line_total']
    list_filter = ['purchase_order__status', 'taxable', 'unit_of_measure']
    search_fields = ['purchase_order__po_number', 'product__name', 'description']


@admin.register(PaymentTerm)
class PaymentTermAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'days', 'is_default', 'is_active']
    list_filter = ['is_default', 'is_active']
    search_fields = ['name', 'code']


class VendorBillCreditItemInline(admin.TabularInline):
    model = VendorBillCreditItem
    extra = 1
    fields = [
        'product', 'return_item', 'quantity_credited', 'unit_price',
        'line_total', 'tax_rate', 'tax_amount', 'description'
    ]
    readonly_fields = ['line_total', 'tax_amount']


@admin.register(VendorBillCredit)
class VendorBillCreditAdmin(admin.ModelAdmin):
    list_display = [
        'credit_number', 'vendor', 'goods_return_note', 'total_credit',
        'status', 'credit_date', 'created_at'
    ]
    list_filter = ['status', 'credit_type', 'credit_date', 'created_at']
    search_fields = [
        'credit_number', 'vendor__name', 'goods_return_note__grn_return_number', 'reason'
    ]
    readonly_fields = [
        'credit_id', 'credit_number', 'remaining_credit', 'created_at', 'updated_at'
    ]
    inlines = [VendorBillCreditItemInline]
    date_hierarchy = 'credit_date'

    fieldsets = (
        ('Basic Information', {
            'fields': (
                'credit_id', 'credit_number', 'vendor', 'goods_return_note',
                'original_vendor_bill'
            )
        }),
        ('Financial Information', {
            'fields': (
                'credit_amount', 'tax_amount', 'total_credit',
                'applied_amount', 'remaining_credit'
            )
        }),
        ('Details', {
            'fields': ('credit_type', 'credit_date', 'status', 'reason', 'notes')
        }),
        ('Approval & Application', {
            'fields': (
                'approved_date', 'approved_by', 'applied_date', 'applied_by'
            )
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at', 'created_by')
        }),
    )





class VendorBillItemInline(admin.TabularInline):
    """Inline admin for Vendor Bill Items"""
    model = VendorBillItem
    extra = 1
    fields = ('product', 'item_description', 'quantity', 'unit_price', 'line_total', 'tax_rate', 'tax_amount', 'account_code')
    readonly_fields = ('line_total', 'tax_amount')


@admin.register(VendorBill)
class VendorBillAdmin(admin.ModelAdmin):
    list_display = ['bill_number', 'vendor', 'bill_date', 'due_date', 'total_amount', 'status', 'purchase_order', 'grn', 'created_at']
    list_filter = ['status', 'bill_date', 'due_date', 'created_at']
    search_fields = ['bill_number', 'vendor__name', 'reference_number', 'notes']
    readonly_fields = ['bill_id', 'bill_number', 'subtotal', 'tax_amount', 'total_amount', 'balance_due', 'created_at', 'updated_at']
    inlines = [VendorBillItemInline]

    fieldsets = (
        ('Basic Information', {
            'fields': ('bill_id', 'bill_number', 'vendor', 'bill_date', 'due_date', 'reference_number')
        }),
        ('Document Links', {
            'fields': ('purchase_order', 'grn', 'goods_return_note'),
            'description': 'Link to source documents for bill creation'
        }),
        ('Financial Information', {
            'fields': ('subtotal', 'tax_amount', 'total_amount', 'amount_paid', 'balance_due')
        }),
        ('Settings', {
            'fields': ('status', 'payment_terms')
        }),
        ('Additional Information', {
            'fields': ('notes',)
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at', 'created_by')
        }),
    )


@admin.register(VendorBillItem)
class VendorBillItemAdmin(admin.ModelAdmin):
    list_display = ['vendor_bill', 'product', 'item_description', 'quantity', 'unit_price', 'line_total', 'account_code']
    list_filter = ['vendor_bill__status', 'tax_rate', 'account_code']
    search_fields = ['vendor_bill__bill_number', 'product__name', 'item_description', 'account_code']
