import React, { useState, useEffect, useCallback } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>ton,
  Alert,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Tooltip,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
} from '@mui/material';
import {
  CreditCard as CreditIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Visibility as ViewIcon,
  Delete as DeleteIcon,
  PostAdd as PostIcon,
  MoreVert as MoreVertIcon,
  Refresh as RefreshIcon,
  Download as DownloadIcon,
  CheckCircle as CheckCircleIcon,
  ThumbUp as ApproveIcon,
  AttachMoney as ApplyIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { PageContainer, PageHeader } from '../../../layouts/components/PageComponents';
import DataTable from '../../../shared/components/DataTable';
import { formatCurrency, formatDate } from '../../../shared/utils/formatters';
import { vendorCreditService, VendorCredit } from '../services/vendor-credit.service';
import { useCurrencyInfo } from '../../gl/hooks/useCurrencyInfo';

const VendorCreditsPage: React.FC = () => {
  const navigate = useNavigate();
  const { currencyInfo, loading: currencyLoading } = useCurrencyInfo();
  const [credits, setCredits] = useState<VendorCredit[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedCredit, setSelectedCredit] = useState<VendorCredit | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [approveDialogOpen, setApproveDialogOpen] = useState(false);
  const [applyDialogOpen, setApplyDialogOpen] = useState(false);
  const [postDialogOpen, setPostDialogOpen] = useState(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  
  // Pagination and filtering
  const [page, setPage] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [creditReasonFilter, setCreditReasonFilter] = useState('');

  // IFRS-compliant currency formatting function
  const formatCurrencyAmount = (amount: number): string => {
    if (!currencyInfo || currencyLoading) {
      console.log('💰 Currency info not loaded yet, using fallback');
      return `₹${amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
    }
    
    console.log('💰 Using currency info:', currencyInfo);
    const symbol = currencyInfo.functional_currency_symbol || '$';
    return `${symbol}${amount.toLocaleString('en-US', { 
      minimumFractionDigits: 2, 
      maximumFractionDigits: 2 
    })}`;
  };

  // Helper function to get product names from credit items
  const getProductNames = (vendorCredit: VendorCredit): { display: string; full: string[] } => {
    if (!vendorCredit.items || vendorCredit.items.length === 0) {
      return { display: 'No products', full: [] };
    }

    const productNames = vendorCredit.items
      .map(item => item.product_name || `Product ${item.product}`)
      .filter(Boolean);

    if (productNames.length <= 3) {
      return { 
        display: productNames.join(', '), 
        full: productNames 
      };
    }

    const displayed = productNames.slice(0, 3);
    const remaining = productNames.length - 3;
    return { 
      display: `${displayed.join(', ')} +${remaining} more`, 
      full: productNames 
    };
  };

  // Status color mapping
  const getStatusColor = (status: string): 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning' => {
    switch (status?.toUpperCase()) {
      case 'DRAFT': return 'default';
      case 'APPROVED': return 'info';
      case 'APPLIED': return 'primary';
      case 'POSTED': return 'success';
      case 'CANCELLED': return 'error';
      default: return 'default';
    }
  };

  // Credit reason color mapping
  const getCreditReasonColor = (reason: string): 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning' => {
    switch (reason?.toUpperCase()) {
      case 'DEFECTIVE': return 'error';
      case 'WRONG_ITEM': return 'warning';
      case 'EXCESS_QTY': return 'info';
      case 'QUALITY_ISSUE': return 'error';
      case 'NOT_ORDERED': return 'warning';
      case 'EXPIRED': return 'error';
      case 'OTHER': return 'default';
      default: return 'default';
    }
  };

  // Load vendor credits
  const loadCredits = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const params = {
        page: page + 1,
        page_size: pageSize,
        search: searchTerm || undefined,
        status: statusFilter || undefined,
        credit_reason: creditReasonFilter || undefined,
      };

      console.log('🔄 Loading vendor credits with params:', params);
      const response = await vendorCreditService.getVendorCredits(params);
      console.log('✅ Vendor credits loaded:', response);
      
      setCredits(response.results || []);
      setTotalCount(response.count || 0);
    } catch (err) {
      console.error('❌ Error loading vendor credits:', err);
      setError('Failed to load vendor credits. Please try again.');
    } finally {
      setLoading(false);
    }
  }, [page, pageSize, searchTerm, statusFilter, creditReasonFilter]);

  // Load data on component mount and when dependencies change
  useEffect(() => {
    loadCredits();
  }, [loadCredits]);

  // Handle search
  const handleSearch = (value: string) => {
    setSearchTerm(value);
    setPage(0); // Reset to first page when searching
  };

  // Handle filter changes
  const handleStatusFilterChange = (value: string) => {
    setStatusFilter(value);
    setPage(0);
  };

  const handleCreditReasonFilterChange = (value: string) => {
    setCreditReasonFilter(value);
    setPage(0);
  };

  // Handle refresh
  const handleRefresh = () => {
    loadCredits();
  };

  // Handle menu actions
  const handleMenuClick = (event: React.MouseEvent<HTMLElement>, credit: VendorCredit) => {
    setAnchorEl(event.currentTarget);
    setSelectedCredit(credit);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedCredit(null);
  };

  // Navigation handlers
  const handleView = (credit: VendorCredit) => {
    navigate(`/dashboard/purchases/vendor-credits/${credit.vendor_credit_id}`);
    handleMenuClose();
  };

  const handleEdit = (credit: VendorCredit) => {
    navigate(`/dashboard/purchases/vendor-credits/${credit.vendor_credit_id}/edit`);
    handleMenuClose();
  };

  const handleCreateNew = () => {
    navigate('/dashboard/purchases/vendor-credits/new');
  };

  // Action handlers
  const handleApprove = (credit: VendorCredit) => {
    setSelectedCredit(credit);
    setApproveDialogOpen(true);
    handleMenuClose();
  };

  const handleApply = (credit: VendorCredit) => {
    setSelectedCredit(credit);
    setApplyDialogOpen(true);
    handleMenuClose();
  };

  const handlePost = (credit: VendorCredit) => {
    setSelectedCredit(credit);
    setPostDialogOpen(true);
    handleMenuClose();
  };

  const handleDelete = (credit: VendorCredit) => {
    setSelectedCredit(credit);
    setDeleteDialogOpen(true);
    handleMenuClose();
  };

  // Confirm approve
  const confirmApprove = async () => {
    if (!selectedCredit) return;
    
    try {
      await vendorCreditService.approveCredit(selectedCredit.vendor_credit_id);
      setApproveDialogOpen(false);
      setSelectedCredit(null);
      loadCredits();
    } catch (err) {
      console.error('Error approving credit:', err);
      setError('Failed to approve credit. Please try again.');
    }
  };

  // Confirm apply
  const confirmApply = async () => {
    if (!selectedCredit) return;
    
    try {
      await vendorCreditService.applyCredit(selectedCredit.vendor_credit_id);
      setApplyDialogOpen(false);
      setSelectedCredit(null);
      loadCredits();
    } catch (err) {
      console.error('Error applying credit:', err);
      setError('Failed to apply credit. Please try again.');
    }
  };

  // Confirm post
  const confirmPost = async () => {
    if (!selectedCredit) return;
    
    try {
      await vendorCreditService.postCredit(selectedCredit.vendor_credit_id);
      setPostDialogOpen(false);
      setSelectedCredit(null);
      loadCredits();
    } catch (err) {
      console.error('Error posting credit:', err);
      setError('Failed to post credit. Please try again.');
    }
  };

  // Confirm delete
  const confirmDelete = async () => {
    if (!selectedCredit) return;
    
    try {
      await vendorCreditService.deleteCredit(selectedCredit.vendor_credit_id);
      setDeleteDialogOpen(false);
      setSelectedCredit(null);
      loadCredits();
    } catch (err) {
      console.error('Error deleting credit:', err);
      setError('Failed to delete credit. Please try again.');
    }
  };

  // Define table columns
  const columns = [
    {
      field: 'vendor_credit_number',
      headerName: 'Credit Number',
      width: 150,
      renderCell: (params: any) => (
        <Button
          variant="text"
          color="primary"
          onClick={() => handleView(params.row)}
          sx={{ textTransform: 'none', justifyContent: 'flex-start', p: 0 }}
        >
          {params.value}
        </Button>
      ),
    },
    {
      field: 'vendor_name',
      headerName: 'Vendor',
      width: 200,
      valueGetter: (params: any) => params.row.vendor?.name || 'No Vendor',
    },
    {
      field: 'credit_date',
      headerName: 'Credit Date',
      width: 120,
      renderCell: (params: any) => formatDate(params.value),
    },
    {
      field: 'products',
      headerName: 'Products',
      width: 250,
      renderCell: (params: any) => {
        const { display, full } = getProductNames(params.row);
        return (
          <Tooltip title={full.join(', ')} arrow>
            <Typography variant="body2" noWrap>
              {display}
            </Typography>
          </Tooltip>
        );
      },
    },
    {
      field: 'credit_reason',
      headerName: 'Reason',
      width: 130,
      renderCell: (params: any) => (
        <Chip
          label={params.value?.replace('_', ' ') || 'N/A'}
          color={getCreditReasonColor(params.value)}
          size="small"
          variant="outlined"
        />
      ),
    },
    {
      field: 'total_credit_amount',
      headerName: 'Credit Amount',
      width: 130,
      renderCell: (params: any) => (
        <Typography variant="body2" fontWeight="medium">
          {formatCurrencyAmount(params.value || 0)}
        </Typography>
      ),
    },
    {
      field: 'remaining_credit',
      headerName: 'Remaining',
      width: 130,
      renderCell: (params: any) => (
        <Typography variant="body2" color={params.value > 0 ? 'success.main' : 'text.secondary'}>
          {formatCurrencyAmount(params.value || 0)}
        </Typography>
      ),
    },
    {
      field: 'status',
      headerName: 'Status',
      width: 120,
      renderCell: (params: any) => (
        <Chip
          label={params.value || 'DRAFT'}
          color={getStatusColor(params.value)}
          size="small"
        />
      ),
    },
    {
      field: 'actions',
      headerName: 'Actions',
      width: 80,
      sortable: false,
      renderCell: (params: any) => (
        <IconButton
          size="small"
          onClick={(e) => handleMenuClick(e, params.row)}
        >
          <MoreVertIcon />
        </IconButton>
      ),
    },
  ];

  return (
    <PageContainer>
      <PageHeader
        title="Vendor Credits"
        action={
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleCreateNew}
          >
            New Credit
          </Button>
        }
      />

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {/* Filters */}
      <Box sx={{ mb: 3, display: 'flex', gap: 2, flexWrap: 'wrap', alignItems: 'center' }}>
        <TextField
          placeholder="Search credits..."
          value={searchTerm}
          onChange={(e) => handleSearch(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
          sx={{ minWidth: 250 }}
        />

        <FormControl sx={{ minWidth: 150 }}>
          <InputLabel>Status</InputLabel>
          <Select
            value={statusFilter}
            onChange={(e) => handleStatusFilterChange(e.target.value)}
            label="Status"
          >
            <MenuItem value="">All Statuses</MenuItem>
            <MenuItem value="DRAFT">Draft</MenuItem>
            <MenuItem value="APPROVED">Approved</MenuItem>
            <MenuItem value="APPLIED">Applied</MenuItem>
            <MenuItem value="POSTED">Posted</MenuItem>
            <MenuItem value="CANCELLED">Cancelled</MenuItem>
          </Select>
        </FormControl>

        <FormControl sx={{ minWidth: 150 }}>
          <InputLabel>Reason</InputLabel>
          <Select
            value={creditReasonFilter}
            onChange={(e) => handleCreditReasonFilterChange(e.target.value)}
            label="Reason"
          >
            <MenuItem value="">All Reasons</MenuItem>
            <MenuItem value="DEFECTIVE">Defective</MenuItem>
            <MenuItem value="WRONG_ITEM">Wrong Item</MenuItem>
            <MenuItem value="EXCESS_QTY">Excess Qty</MenuItem>
            <MenuItem value="QUALITY_ISSUE">Quality Issue</MenuItem>
            <MenuItem value="NOT_ORDERED">Not Ordered</MenuItem>
            <MenuItem value="EXPIRED">Expired</MenuItem>
            <MenuItem value="OTHER">Other</MenuItem>
          </Select>
        </FormControl>

        <Button
          variant="outlined"
          startIcon={<RefreshIcon />}
          onClick={handleRefresh}
        >
          Refresh
        </Button>
      </Box>

      {/* Data Table */}
      <DataTable
        rows={credits}
        columns={columns}
        loading={loading}
        page={page}
        pageSize={pageSize}
        totalCount={totalCount}
        onPageChange={setPage}
        onPageSizeChange={setPageSize}
        getRowId={(row) => row.vendor_credit_id}
      />

      {/* Action Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => selectedCredit && handleView(selectedCredit)}>
          <ListItemIcon>
            <ViewIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>View Details</ListItemText>
        </MenuItem>

        {selectedCredit?.status === 'DRAFT' && (
          <>
            <MenuItem onClick={() => selectedCredit && handleEdit(selectedCredit)}>
              <ListItemIcon>
                <EditIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText>Edit</ListItemText>
            </MenuItem>
            <MenuItem onClick={() => selectedCredit && handleApprove(selectedCredit)}>
              <ListItemIcon>
                <ApproveIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText>Approve</ListItemText>
            </MenuItem>
            <MenuItem onClick={() => selectedCredit && handleDelete(selectedCredit)}>
              <ListItemIcon>
                <DeleteIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText>Delete</ListItemText>
            </MenuItem>
          </>
        )}

        {selectedCredit?.status === 'APPROVED' && (
          <MenuItem onClick={() => selectedCredit && handleApply(selectedCredit)}>
            <ListItemIcon>
              <ApplyIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>Apply to Bills</ListItemText>
          </MenuItem>
        )}

        {selectedCredit?.status === 'APPLIED' && (
          <MenuItem onClick={() => selectedCredit && handlePost(selectedCredit)}>
            <ListItemIcon>
              <PostIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>Post to GL</ListItemText>
          </MenuItem>
        )}
      </Menu>

      {/* Approve Dialog */}
      <Dialog open={approveDialogOpen} onClose={() => setApproveDialogOpen(false)}>
        <DialogTitle>Approve Vendor Credit</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to approve vendor credit {selectedCredit?.vendor_credit_number}?
            This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setApproveDialogOpen(false)}>Cancel</Button>
          <Button onClick={confirmApprove} variant="contained" color="primary">
            Approve
          </Button>
        </DialogActions>
      </Dialog>

      {/* Apply Dialog */}
      <Dialog open={applyDialogOpen} onClose={() => setApplyDialogOpen(false)}>
        <DialogTitle>Apply Vendor Credit</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to apply vendor credit {selectedCredit?.vendor_credit_number} to vendor bills?
            This will make the credit available for bill payments.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setApplyDialogOpen(false)}>Cancel</Button>
          <Button onClick={confirmApply} variant="contained" color="primary">
            Apply
          </Button>
        </DialogActions>
      </Dialog>

      {/* Post Dialog */}
      <Dialog open={postDialogOpen} onClose={() => setPostDialogOpen(false)}>
        <DialogTitle>Post Vendor Credit</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to post vendor credit {selectedCredit?.vendor_credit_number} to the General Ledger?
            This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPostDialogOpen(false)}>Cancel</Button>
          <Button onClick={confirmPost} variant="contained" color="primary">
            Post
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>Delete Vendor Credit</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete vendor credit {selectedCredit?.vendor_credit_number}?
            This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button onClick={confirmDelete} variant="contained" color="error">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </PageContainer>
  );
};

export default VendorCreditsPage;
