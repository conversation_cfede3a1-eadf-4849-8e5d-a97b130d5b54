import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import {
  Box,
  Button,
  Card,
  CardContent,
  Chip,
  FormControl,
  Grid,
  IconButton,
  InputLabel,
  List,
  ListItem,
  ListItemText,
  MenuItem,
  Paper,
  Select,
  TextField,
  Typography,
  Collapse,
  Divider
} from '@mui/material';
import {
  Add as AddIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  Description as DocumentIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Schedule as ClockIcon,
  CreditCard as CreditCardIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon
} from '@mui/icons-material';
import { VendorCredit, VendorCreditFilters, CREDIT_STATUS_COLORS, CREDIT_TYPE_COLORS } from '../../../types/vendorCredit';
import vendorCreditService from '../../../services/vendorCredit.service';
import LoadingSpinner from '../../../shared/components/LoadingSpinner';
import Pagination from '../../../shared/components/Pagination';

const VendorCreditsPage: React.FC = () => {
  const navigate = useNavigate();
  const [credits, setCredits] = useState<VendorCredit[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState<VendorCreditFilters>({});
  const [currentPage, setCurrentPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [showFilters, setShowFilters] = useState(false);

  const itemsPerPage = 10;

  useEffect(() => {
    fetchCredits();
  }, [currentPage, filters, searchTerm]);

  const fetchCredits = async () => {
    try {
      setLoading(true);
      const response = await vendorCreditService.getVendorCredits({
        ...filters,
        search: searchTerm || undefined,
        page: currentPage,
        page_size: itemsPerPage,
      });
      setCredits(response.results);
      setTotalCount(response.count);
      setError(null);
    } catch (err) {
      setError('Failed to fetch vendor credits');
      console.error('Error fetching credits:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1);
    fetchCredits();
  };

  const handleFilterChange = (key: keyof VendorCreditFilters, value: string) => {
    setFilters(prev => ({
      ...prev,
      [key]: value || undefined
    }));
    setCurrentPage(1);
  };

  const clearFilters = () => {
    setFilters({});
    setSearchTerm('');
    setCurrentPage(1);
  };

  const handleApproveCredit = async (creditId: number) => {
    try {
      await vendorCreditService.approveCredit(creditId);
      fetchCredits(); // Refresh the list
    } catch (err) {
      console.error('Error approving credit:', err);
      alert('Failed to approve credit');
    }
  };

  const handleApplyCredit = async (creditId: number) => {
    try {
      await vendorCreditService.applyCredit(creditId);
      fetchCredits(); // Refresh the list
    } catch (err) {
      console.error('Error applying credit:', err);
      alert('Failed to apply credit');
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
        return <CheckCircleIcon color="success" />;
      case 'applied':
        return <CheckCircleIcon color="primary" />;
      case 'cancelled':
        return <CancelIcon color="error" />;
      default:
        return <ClockIcon color="warning" />;
    }
  };

  const getStatusColor = (status: string): 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning' => {
    switch (status) {
      case 'approved': return 'success';
      case 'applied': return 'primary';
      case 'cancelled': return 'error';
      default: return 'warning';
    }
  };

  const getCreditTypeColor = (type: string): 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning' => {
    switch (type) {
      case 'goods_return': return 'secondary';
      case 'price_adjustment': return 'info';
      case 'damaged_goods': return 'error';
      case 'billing_error': return 'warning';
      case 'discount': return 'success';
      default: return 'default';
    }
  };

  if (loading && credits.length === 0) {
    return <LoadingSpinner />;
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom>
            Vendor Credits
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Manage vendor bill credits from goods returns
          </Typography>
        </Box>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => navigate('/dashboard/purchase/vendor-credits/new')}
        >
          New Credit
        </Button>
      </Box>

      {/* Search and Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box display="flex" gap={2} alignItems="center" mb={2}>
            {/* Search */}
            <TextField
              fullWidth
              variant="outlined"
              placeholder="Search credits..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSearch(e)}
              InputProps={{
                startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
              }}
            />

            {/* Filter Toggle */}
            <Button
              variant="outlined"
              startIcon={<FilterIcon />}
              endIcon={showFilters ? <ExpandLessIcon /> : <ExpandMoreIcon />}
              onClick={() => setShowFilters(!showFilters)}
            >
              Filters
            </Button>
          </Box>

          {/* Filters */}
          <Collapse in={showFilters}>
            <Divider sx={{ my: 2 }} />
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6} md={3}>
                <FormControl fullWidth>
                  <InputLabel>Status</InputLabel>
                  <Select
                    value={filters.status || ''}
                    onChange={(e) => handleFilterChange('status', e.target.value)}
                    label="Status"
                  >
                    <MenuItem value="">All Statuses</MenuItem>
                    <MenuItem value="draft">Draft</MenuItem>
                    <MenuItem value="approved">Approved</MenuItem>
                    <MenuItem value="applied">Applied</MenuItem>
                    <MenuItem value="cancelled">Cancelled</MenuItem>
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <FormControl fullWidth>
                  <InputLabel>Credit Type</InputLabel>
                  <Select
                    value={filters.credit_type || ''}
                    onChange={(e) => handleFilterChange('credit_type', e.target.value)}
                    label="Credit Type"
                  >
                    <MenuItem value="">All Types</MenuItem>
                    <MenuItem value="goods_return">Goods Return</MenuItem>
                    <MenuItem value="price_adjustment">Price Adjustment</MenuItem>
                    <MenuItem value="damaged_goods">Damaged Goods</MenuItem>
                    <MenuItem value="billing_error">Billing Error</MenuItem>
                    <MenuItem value="discount">Discount</MenuItem>
                    <MenuItem value="other">Other</MenuItem>
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  fullWidth
                  type="date"
                  label="Start Date"
                  value={filters.start_date || ''}
                  onChange={(e) => handleFilterChange('start_date', e.target.value)}
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  fullWidth
                  type="date"
                  label="End Date"
                  value={filters.end_date || ''}
                  onChange={(e) => handleFilterChange('end_date', e.target.value)}
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>

              <Grid item xs={12}>
                <Button
                  onClick={clearFilters}
                  color="primary"
                  size="small"
                >
                  Clear all filters
                </Button>
              </Grid>
            </Grid>
          </Collapse>
        </CardContent>
      </Card>

      {/* Error Message */}
      {error && (
        <Paper sx={{ p: 2, mb: 2, bgcolor: 'error.light', color: 'error.contrastText' }}>
          <Typography variant="body2">{error}</Typography>
        </Paper>
      )}

      {/* Credits List */}
      <Card>
        <List>
          {credits.map((credit, index) => (
            <React.Fragment key={credit.id}>
              {index > 0 && <Divider />}
              <ListItem
                sx={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  py: 2
                }}
              >
                <Box display="flex" alignItems="center" flex={1}>
                  <Box mr={2}>
                    {getStatusIcon(credit.status)}
                  </Box>
                  <Box flex={1}>
                    <Box display="flex" alignItems="center" gap={1} mb={1}>
                      <Typography
                        variant="subtitle1"
                        color="primary"
                        component={Link}
                        to={`/dashboard/purchase/vendor-credits/${credit.id}`}
                        sx={{ textDecoration: 'none', '&:hover': { textDecoration: 'underline' } }}
                      >
                        {credit.credit_number}
                      </Typography>
                      <Chip
                        label={credit.status}
                        color={getStatusColor(credit.status)}
                        size="small"
                      />
                      <Chip
                        label={credit.credit_type.replace('_', ' ')}
                        color={getCreditTypeColor(credit.credit_type)}
                        size="small"
                        variant="outlined"
                      />
                    </Box>
                    <Typography variant="body2" color="text.secondary">
                      {credit.vendor_name} • {vendorCreditService.formatCurrency(credit.total_credit)}
                      {credit.goods_return_number && ` • Return: ${credit.goods_return_number}`}
                    </Typography>
                  </Box>
                </Box>
                <Box display="flex" gap={1}>
                  {credit.status === 'draft' && (
                    <Button
                      size="small"
                      variant="contained"
                      color="success"
                      onClick={() => handleApproveCredit(credit.id!)}
                    >
                      Approve
                    </Button>
                  )}
                  {credit.status === 'approved' && (
                    <Button
                      size="small"
                      variant="contained"
                      color="primary"
                      onClick={() => handleApplyCredit(credit.id!)}
                    >
                      Apply
                    </Button>
                  )}
                  <Button
                    size="small"
                    variant="outlined"
                    startIcon={<DocumentIcon />}
                    component={Link}
                    to={`/dashboard/purchase/vendor-credits/${credit.id}`}
                  >
                    View
                  </Button>
                </Box>
              </ListItem>
            </React.Fragment>
          ))}
        </List>

        {credits.length === 0 && !loading && (
          <Box textAlign="center" py={6}>
            <CreditCardIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" gutterBottom>
              No vendor credits
            </Typography>
            <Typography variant="body2" color="text.secondary" mb={3}>
              Get started by creating a new vendor credit.
            </Typography>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => navigate('/dashboard/purchase/vendor-credits/new')}
            >
              New Credit
            </Button>
          </Box>
        )}
      </Card>

      {/* Pagination */}
      {totalCount > itemsPerPage && (
        <Pagination
          currentPage={currentPage}
          totalPages={Math.ceil(totalCount / itemsPerPage)}
          onPageChange={setCurrentPage}
          totalItems={totalCount}
          itemsPerPage={itemsPerPage}
        />
      )}
    </Box>
  );
};

export default VendorCreditsPage;
